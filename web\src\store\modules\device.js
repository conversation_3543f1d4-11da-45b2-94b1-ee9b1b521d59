import axios from 'axios'

const state = {
  devices: [],
  selectedDevices: [],
  loading: false,
  // 设备执行状态跟踪：记录每个设备当前正在执行的功能
  deviceExecutionStatus: {} // { deviceId: { functionType: 'xiaohongshu_profile', module: 'xiaoh<PERSON><PERSON>', startTime: Date } }
}

const getters = {
  devices: state => state.devices,
  selectedDevices: state => state.selectedDevices,
  onlineDevices: state => state.devices.filter(device => device.status === 'online'),
  offlineDevices: state => state.devices.filter(device => device.status === 'offline'),
  busyDevices: state => state.devices.filter(device => device.status === 'busy'),
  loading: state => state.loading,

  // 获取设备执行状态
  deviceExecutionStatus: state => state.deviceExecutionStatus,

  // 获取指定设备的执行状态
  getDeviceExecutionStatus: (state) => (deviceId) => {
    return state.deviceExecutionStatus[deviceId] || null
  },

  // 检查设备是否正在执行指定功能
  isDeviceExecutingFunction: (state) => (deviceId, functionType) => {
    const status = state.deviceExecutionStatus[deviceId]
    return status && status.functionType === functionType
  },

  // 检查设备是否正在执行任何功能
  isDeviceExecutingAnyFunction: (state) => (deviceId) => {
    return !!state.deviceExecutionStatus[deviceId]
  },

  // 获取正在执行指定模块功能的设备列表
  getDevicesExecutingModule: (state) => (module) => {
    const executingDevices = []
    Object.keys(state.deviceExecutionStatus).forEach(deviceId => {
      const status = state.deviceExecutionStatus[deviceId]
      if (status && status.module === module) {
        executingDevices.push({
          deviceId,
          functionType: status.functionType,
          startTime: status.startTime
        })
      }
    })
    return executingDevices
  }
}

const mutations = {
  SET_DEVICES(state, devices) {
    state.devices = devices
  },
  ADD_DEVICE(state, device) {
    const existingIndex = state.devices.findIndex(d => d.device_id === device.device_id)
    if (existingIndex >= 0) {
      state.devices.splice(existingIndex, 1, device)
    } else {
      state.devices.push(device)
    }
  },
  UPDATE_DEVICE(state, { deviceId, updates }) {
    const device = state.devices.find(d => d.device_id === deviceId)
    if (device) {
      Object.assign(device, updates)
      console.log(`Store中设备 ${deviceId} 状态已更新:`, updates)
    } else {
      console.warn(`Store中未找到设备 ${deviceId}，无法更新状态`)
    }
  },
  UPDATE_DEVICE_STATUS(state, { deviceId, status, lastSeen }) {
    const device = state.devices.find(d => d.device_id === deviceId)
    if (device) {
      device.status = status
      if (lastSeen) {
        device.last_seen = lastSeen
      }
      console.log(`[Device Store] 设备状态已更新: ${deviceId} -> ${status}`)
    } else {
      console.log(`[Device Store] 设备未找到，无法更新状态: ${deviceId}`)
    }
  },
  REMOVE_DEVICE(state, deviceId) {
    const index = state.devices.findIndex(d => d.device_id === deviceId)
    if (index >= 0) {
      state.devices.splice(index, 1)
    }
  },
  SET_SELECTED_DEVICES(state, deviceIds) {
    state.selectedDevices = deviceIds
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },

  // 设置设备执行状态
  SET_DEVICE_EXECUTION_STATUS(state, { deviceId, functionType, module, startTime }) {
    if (functionType && module) {
      state.deviceExecutionStatus[deviceId] = {
        functionType,
        module,
        startTime: startTime || new Date()
      }
      // 只在开发环境输出详细日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Device Store] 设备 ${deviceId} 开始执行功能: ${functionType} (${module})`)
      }
    } else {
      // 清除执行状态
      delete state.deviceExecutionStatus[deviceId]
      // 只在开发环境输出详细日志
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Device Store] 清除设备 ${deviceId} 的执行状态`)
      }
    }
  },

  // 清除设备执行状态
  CLEAR_DEVICE_EXECUTION_STATUS(state, deviceId) {
    delete state.deviceExecutionStatus[deviceId]
    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Device Store] 清除设备 ${deviceId} 的执行状态`)
    }
  },

  // 清除所有设备执行状态
  CLEAR_ALL_DEVICE_EXECUTION_STATUS(state) {
    state.deviceExecutionStatus = {}
    // 只在开发环境输出详细日志
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Device Store] 清除所有设备执行状态`)
    }
  }
}

const actions = {
  // 获取设备列表
  async fetchDevices({ commit }) {
    commit('SET_LOADING', true)
    try {
      const response = await axios.get('/api/device/list')
      if (response.data.success) {
        // 将API响应的驼峰命名字段映射为下划线命名字段
        const mappedDevices = response.data.data.map(device => ({
          device_id: device.deviceId,
          device_name: device.deviceName,
          device_info: device.deviceInfo,
          status: device.status,
          last_seen: device.lastActiveTime,
          created_at: device.createdAt,
          ip_address: device.deviceIP || '未知',
          is_connected: device.isConnected
        }))

        console.log('[Device Store] 设备列表字段映射完成:', mappedDevices.length)
        commit('SET_DEVICES', mappedDevices)
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 断开设备连接（不删除记录）
  async deleteDevice({ commit }, deviceId) {
    try {
      const response = await axios.delete(`/api/device/${deviceId}`)
      if (response.data.success) {
        // 断开连接时更新设备状态为离线，而不是移除设备
        commit('UPDATE_DEVICE', { deviceId, updates: { status: 'offline' } })
        return { success: true, message: response.data.message }
      }
    } catch (error) {
      throw error
    }
  },

  // 删除设备记录（真正删除）
  async deleteDeviceRecord({ commit }, deviceId) {
    try {
      const response = await axios.delete(`/api/device/${deviceId}/delete`)
      if (response.data.success) {
        commit('REMOVE_DEVICE', deviceId)
        return { success: true, message: response.data.message }
      }
    } catch (error) {
      throw error
    }
  },

  // 获取设备详情
  async getDeviceDetail({ commit }, deviceId) {
    try {
      const response = await axios.get(`/api/device/${deviceId}`)
      if (response.data.success) {
        return response.data.data
      }
    } catch (error) {
      throw error
    }
  },

  // 获取设备日志
  async getDeviceLogs({ commit }, { deviceId, page = 1, limit = 20 }) {
    try {
      const response = await axios.get(`/api/device/${deviceId}/logs`, {
        params: { page, limit }
      })
      if (response.data.success) {
        return response.data.data
      }
    } catch (error) {
      throw error
    }
  },

  // 设备上线
  deviceOnline({ commit }, device) {
    commit('ADD_DEVICE', { ...device, status: 'online' })
  },

  // 设备离线
  deviceOffline({ commit }, deviceId) {
    commit('UPDATE_DEVICE', { deviceId, updates: { status: 'offline' } })
  },

  // 更新设备状态
  updateDeviceStatus({ commit }, { deviceId, status, ...updates }) {
    commit('UPDATE_DEVICE', { deviceId, updates: { status, ...updates } })
  },

  // 设置选中的设备
  setSelectedDevices({ commit }, deviceIds) {
    commit('SET_SELECTED_DEVICES', deviceIds)
  },

  // 设置设备执行状态
  setDeviceExecutionStatus({ commit }, { deviceId, functionType, module, startTime }) {
    commit('SET_DEVICE_EXECUTION_STATUS', { deviceId, functionType, module, startTime })
  },

  // 清除设备执行状态
  clearDeviceExecutionStatus({ commit }, deviceId) {
    commit('CLEAR_DEVICE_EXECUTION_STATUS', deviceId)
  },

  // 清除所有设备执行状态
  clearAllDeviceExecutionStatus({ commit }) {
    commit('CLEAR_ALL_DEVICE_EXECUTION_STATUS')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
