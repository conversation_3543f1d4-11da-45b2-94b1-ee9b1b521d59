/**
 * 用户隔离中间件
 * 负责用户身份验证、数据过滤和权限控制
 */

const jwt = require('jsonwebtoken');

// JWT密钥（应该从环境变量获取）
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

/**
 * 用户隔离中间件主函数
 * @param {Object} req - Express请求对象
 * @param {Object} res - Express响应对象
 * @param {Function} next - Express下一个中间件函数
 */
const userIsolationMiddleware = (req, res, next) => {
  try {
    // 1. 提取JWT token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ 
        success: false,
        message: '访问令牌缺失' 
      });
    }
    
    // 2. 验证token并提取用户信息
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    req.currentUserId = decoded.userId;
    
    // 3. 添加访问日志（仅记录重要操作，避免频繁日志）
    if (req.method !== 'GET' || req.path.includes('/execute') || req.path.includes('/stop')) {
      console.log(`[数据隔离] 用户 ${decoded.username}(ID:${decoded.userId}) 访问: ${req.method} ${req.path}`);
    }

    // 3.5. 跟踪活跃用户（用于设备迁移）
    if (req.app.locals.activeUsers) {
      req.app.locals.activeUsers.set(decoded.userId, {
        userId: decoded.userId,
        username: decoded.username,
        lastActivity: new Date(),
        userAgent: req.headers['user-agent'] || 'unknown'
      });
    }

    // 4. 为请求对象添加用户过滤工具方法
    req.addUserFilter = (sql, params = []) => {
      return addUserFilterToQuery(sql, params, decoded.userId);
    };
    
    // 5. 为请求对象添加权限验证方法
    req.validateOwnership = async (resourceType, resourceId, pool) => {
      return await validateResourceOwnership(resourceType, resourceId, decoded.userId, pool);
    };
    
    next();
  } catch (error) {
    console.error('[数据隔离] Token验证失败:', error.message);
    return res.status(403).json({ 
      success: false,
      message: '访问令牌无效' 
    });
  }
};

/**
 * 需要用户隔离的数据表列表
 */
const ISOLATED_TABLES = [
  'devices',
  'device_apps', 
  'xiaohongshu_execution_logs',
  'xianyu_execution_logs',
  'uid_files',
  'uid_data',
  'xiaohongshu_uids',
  'xiaohongshu_manual_uid_messages',
  'xiaohongshu_file_uid_messages',
  'xianyu_chat_records',
  'xiaohongshu_video_files',
  'xiaohongshu_video_transfers',
  'xiaohongshu_video_assignments',
  'xiaohongshu_video_execution_logs',
  'scripts',
  'execution_logs',
  'file_transfers'
];

/**
 * 检查SQL语句是否需要添加用户过滤
 * @param {string} sql - SQL语句
 * @returns {boolean} 是否需要用户过滤
 */
function needsUserFilter(sql) {
  const upperSql = sql.toUpperCase();
  return ISOLATED_TABLES.some(table => 
    upperSql.includes(`FROM ${table.toUpperCase()}`) || 
    upperSql.includes(`JOIN ${table.toUpperCase()}`) ||
    upperSql.includes(`UPDATE ${table.toUpperCase()}`) ||
    upperSql.includes(`DELETE FROM ${table.toUpperCase()}`)
  );
}

/**
 * 自动为SQL查询添加用户过滤条件
 * @param {string} sql - 原始SQL语句
 * @param {Array} params - SQL参数数组
 * @param {number} userId - 当前用户ID
 * @returns {Object} 包含增强后的SQL和参数的对象
 */
function addUserFilterToQuery(sql, params, userId) {
  // 如果不需要用户过滤或已经包含user_id条件，直接返回
  if (!needsUserFilter(sql) || sql.toLowerCase().includes('user_id')) {
    return { sql, params };
  }
  
  const upperSql = sql.toUpperCase();
  let enhancedSql = sql;
  let enhancedParams = [...params];
  
  try {
    // 处理SELECT查询
    if (upperSql.includes('SELECT')) {
      if (upperSql.includes('WHERE')) {
        // 已有WHERE子句，在前面添加user_id条件
        enhancedSql = sql.replace(/WHERE/i, 'WHERE user_id = ? AND');
        enhancedParams.unshift(userId);
      } else {
        // 没有WHERE子句，查找FROM后添加WHERE
        const fromMatch = sql.match(/FROM\s+(\w+)/i);
        if (fromMatch) {
          const tableName = fromMatch[1];
          if (ISOLATED_TABLES.includes(tableName.toLowerCase())) {
            enhancedSql = sql.replace(/FROM\s+(\w+)/i, `FROM $1 WHERE user_id = ?`);
            enhancedParams.push(userId);
          }
        }
      }
    }
    
    // 处理UPDATE查询
    else if (upperSql.includes('UPDATE')) {
      if (upperSql.includes('WHERE')) {
        enhancedSql = sql.replace(/WHERE/i, 'WHERE user_id = ? AND');
        enhancedParams.unshift(userId);
      } else {
        enhancedSql += ' WHERE user_id = ?';
        enhancedParams.push(userId);
      }
    }
    
    // 处理DELETE查询
    else if (upperSql.includes('DELETE')) {
      if (upperSql.includes('WHERE')) {
        enhancedSql = sql.replace(/WHERE/i, 'WHERE user_id = ? AND');
        enhancedParams.unshift(userId);
      } else {
        enhancedSql += ' WHERE user_id = ?';
        enhancedParams.push(userId);
      }
    }
    
    // 记录SQL增强日志
    if (enhancedSql !== sql) {
      console.log(`[SQL增强] 为用户${userId}添加过滤条件`);
      console.log(`原始SQL: ${sql}`);
      console.log(`增强SQL: ${enhancedSql}`);
    }
    
  } catch (error) {
    console.error('[SQL增强] 处理失败:', error);
    // 如果处理失败，返回原始SQL（安全降级）
    return { sql, params };
  }
  
  return { 
    sql: enhancedSql, 
    params: enhancedParams 
  };
}

/**
 * 验证资源所属权
 * @param {string} resourceType - 资源类型 (device, file, task等)
 * @param {string} resourceId - 资源ID
 * @param {number} userId - 用户ID
 * @param {Object} pool - 数据库连接池
 * @returns {boolean} 是否拥有权限
 */
async function validateResourceOwnership(resourceType, resourceId, userId, pool) {
  try {
    let query = '';
    let params = [];
    
    switch (resourceType) {
      case 'device':
        query = 'SELECT id FROM devices WHERE device_id = ? AND user_id = ?';
        params = [resourceId, userId];
        break;
        
      case 'uid_file':
        query = 'SELECT id FROM uid_files WHERE id = ? AND user_id = ?';
        params = [resourceId, userId];
        break;
        
      case 'video_file':
        query = 'SELECT id FROM xiaohongshu_video_files WHERE id = ? AND user_id = ?';
        params = [resourceId, userId];
        break;
        
      case 'xiaohongshu_task':
        query = 'SELECT id FROM xiaohongshu_execution_logs WHERE task_id = ? AND user_id = ?';
        params = [resourceId, userId];
        break;
        
      case 'xianyu_task':
        query = 'SELECT id FROM xianyu_execution_logs WHERE task_id = ? AND user_id = ?';
        params = [resourceId, userId];
        break;
        
      case 'script':
        query = 'SELECT id FROM scripts WHERE id = ? AND user_id = ?';
        params = [resourceId, userId];
        break;
        
      default:
        console.warn(`[权限验证] 未知资源类型: ${resourceType}`);
        return false;
    }
    
    const [rows] = await pool.execute(query, params);
    const hasPermission = rows.length > 0;
    
    // 只记录权限验证失败的情况，成功的情况使用节流日志
    if (!hasPermission) {
      console.log(`[权限验证] 用户${userId} 无权访问 ${resourceType}:${resourceId}`);
    }
    
    return hasPermission;
    
  } catch (error) {
    console.error('[权限验证] 验证失败:', error);
    return false; // 验证失败时拒绝访问
  }
}

/**
 * 为数据插入自动添加user_id
 * @param {string} tableName - 表名
 * @param {Object} data - 要插入的数据
 * @param {number} userId - 用户ID
 * @returns {Object} 增强后的数据对象
 */
function addUserIdToInsertData(tableName, data, userId) {
  if (ISOLATED_TABLES.includes(tableName.toLowerCase())) {
    return {
      ...data,
      user_id: userId
    };
  }
  return data;
}

/**
 * 创建带用户过滤的数据库执行器
 * @param {Object} pool - 数据库连接池
 * @param {number} userId - 用户ID
 * @returns {Object} 增强的数据库执行器
 */
function createUserFilteredExecutor(pool, userId) {
  return {
    // 带用户过滤的查询执行
    async execute(sql, params = []) {
      const { sql: enhancedSql, params: enhancedParams } = addUserFilterToQuery(sql, params, userId);
      return await pool.execute(enhancedSql, enhancedParams);
    },
    
    // 带用户ID的插入执行
    async insertWithUserId(tableName, data) {
      const enhancedData = addUserIdToInsertData(tableName, data, userId);
      const fields = Object.keys(enhancedData);
      const values = Object.values(enhancedData);
      const placeholders = fields.map(() => '?').join(', ');
      
      const sql = `INSERT INTO ${tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
      return await pool.execute(sql, values);
    }
  };
}

module.exports = {
  userIsolationMiddleware,
  addUserFilterToQuery,
  validateResourceOwnership,
  addUserIdToInsertData,
  createUserFilteredExecutor,
  ISOLATED_TABLES
};
