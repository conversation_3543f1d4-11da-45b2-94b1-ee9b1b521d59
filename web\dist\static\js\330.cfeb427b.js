"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[330],{7928:function(e,t,s){s.r(t),s.d(t,{default:function(){return f}});var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"xianyu-automation"},[t("div",{staticClass:"page-header"},[t("div",{staticClass:"header-content"},[e._m(0),t("div",{staticClass:"header-actions"},[t("el-button",{attrs:{type:"primary",icon:"el-icon-notebook-1"},on:{click:e.goToLogs}},[e._v(" 查看执行日志 ")])],1)])]),t("el-row",{staticClass:"function-cards",attrs:{gutter:20}},e._l(e.functions,function(s){return t("el-col",{key:s.key,attrs:{span:6}},[t("el-card",{staticClass:"function-card",class:{active:e.selectedFunction===s.key},attrs:{shadow:"hover"},nativeOn:{click:function(t){return e.selectFunction(s.key)}}},[t("div",{staticClass:"card-content"},[t("i",{staticClass:"function-icon",class:s.icon}),t("h3",[e._v(e._s(s.name))]),t("p",[e._v(e._s(s.description))])])])],1)}),1),e.selectedFunction?t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("DeviceInfo",{attrs:{enableBatchSelect:!0,selectedDeviceIds:e.selectedDevices,currentFunction:e.selectedFunction},on:{"device-selected":e.handleDeviceSelected,"device-removed":e.handleDeviceRemoved,"devices-selection-changed":e.handleDevicesSelectionChanged}})],1),t("el-col",{attrs:{span:16}},[t("el-card",{staticClass:"config-panel"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.getCurrentFunctionName())+"配置")]),t("el-button",{staticStyle:{float:"right","margin-top":"-3px","margin-left":"10px"},attrs:{type:"warning",size:"small"},on:{click:e.resetAllStates}},[t("i",{staticClass:"el-icon-refresh-left"}),e._v(" 重置状态 ")]),t("el-button",{staticStyle:{float:"right","margin-top":"-3px"},attrs:{type:"danger",size:"small"},on:{click:e.cleanupTestData}},[t("i",{staticClass:"el-icon-delete"}),e._v(" 清理测试数据 ")])],1),0===e.selectedDevices.length?t("div",{staticClass:"config-section"},[t("el-alert",{attrs:{title:"请先选择执行设备",type:"warning",closable:!1,"show-icon":""}},[e._v(" 请在左侧设备信息中选择要执行脚本的设备 ")])],1):t("div",{staticClass:"config-section"},[t("h4",[e._v("已选设备 ("+e._s(e.selectedDevices.length)+"个)")]),e._l(e.selectedDevices,function(s){return t("el-tag",{key:s,staticStyle:{"margin-right":"8px","margin-bottom":"8px"},attrs:{closable:"",type:e.getDeviceTagType(s)},on:{close:function(t){return e.removeDevice(s)}}},[e._v(" "+e._s(e.getDeviceNameWithStatus(s))+" ")])})],2),e.selectedDevices.length>0?t("div",{staticClass:"config-section"},[e.getComponentName()?t("div",[t("el-tabs",{staticClass:"device-config-tabs",attrs:{type:"card"},on:{"tab-click":e.handleTabClick},model:{value:e.activeDeviceTab,callback:function(t){e.activeDeviceTab=t},expression:"activeDeviceTab"}},e._l(e.selectedDevices,function(s){return t("el-tab-pane",{key:s,attrs:{label:e.getDeviceTabLabel(s),name:s}},[t("div",{staticClass:"device-config-content"},[t("div",{staticClass:"device-config-header"},[t("h5",[e._v(e._s(e.getDeviceNameWithStatus(s))+" - 配置参数")]),t("el-button",{attrs:{type:"primary",size:"small",loading:e.executing,disabled:!e.isDeviceConfigValid(s)},on:{click:function(t){return e.executeForDevice(s)}}},[e._v(" 执行此设备 ")])],1),"keywordMessage"===e.selectedFunction?t("XianyuKeywordMessageConfig",{ref:`config_${s}`,refInFor:!0,attrs:{"device-id":s},on:{"config-change":e.handleConfigChange,"stop-script":e.handleStopScript}}):e._e()],1)])}),1),t("div",{staticClass:"batch-execute-section"},[t("el-button",{attrs:{type:"success",size:"medium",loading:e.executing,disabled:!e.hasValidConfigs(),icon:"el-icon-s-promotion"},on:{click:e.executeAllDevices}},[e._v(" 批量执行所有设备 ("+e._s(e.getValidConfigCount())+"/"+e._s(e.selectedDevices.length)+") ")]),t("el-button",{attrs:{type:"info",size:"medium",disabled:e.selectedDevices.length<=1||!e.activeDeviceTab,icon:"el-icon-document-copy"},on:{click:e.copyConfigToAll}},[e._v(" 复制当前配置到所有设备 ")])],1)],1):t("div",{staticClass:"no-component"},[t("el-alert",{attrs:{title:"组件加载错误",type:"warning",closable:!1,"show-icon":""}},[e._v(" 未找到对应的配置组件："+e._s(e.selectedFunction)+" ")])],1)]):e._e()])],1)],1):e._e(),t("div",{staticClass:"device-stats"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon"},[t("i",{staticClass:"el-icon-mobile-phone"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.connectedDevicesCount))]),t("div",{staticClass:"stat-label"},[e._v("连接设备")])])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon online"},[t("i",{staticClass:"el-icon-success"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.onlineDevicesCount))]),t("div",{staticClass:"stat-label"},[e._v("在线设备")])])])]),t("el-col",{attrs:{span:8}},[t("div",{staticClass:"stat-card"},[t("div",{staticClass:"stat-icon busy"},[t("i",{staticClass:"el-icon-loading"})]),t("div",{staticClass:"stat-content"},[t("div",{staticClass:"stat-value"},[e._v(e._s(e.busyDevicesCount))]),t("div",{staticClass:"stat-label"},[e._v("忙碌设备")])])])])],1)],1)],1)},o=[function(){var e=this,t=e._self._c;return t("div",[t("h2",[e._v("闲鱼自动化工具")]),t("p",[e._v("专业的闲鱼运营自动化工具，支持关键词搜索私信等功能")])])}],n=function(){var e=this,t=e._self._c;return t("div",{staticClass:"xianyu-keyword-message-config"},[t("el-form",{attrs:{model:e.config,"label-width":"120px"}},[t("el-form-item",{attrs:{label:"闲鱼应用"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要使用的闲鱼应用",loading:e.loadingApps},on:{change:e.onAppSelectionChange},model:{value:e.config.selectedApp,callback:function(t){e.$set(e.config,"selectedApp",t)},expression:"config.selectedApp"}},e._l(e.xianyuApps,function(s){return t("el-option",{key:s.text,attrs:{label:s.text,value:s.text}},[t("span",{staticStyle:{float:"left"}},[e._v(e._s(s.text))]),t("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px"}},[e._v(" "+e._s("keyword"===s.method?"关键词":"正则")+" ")])])}),1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 选择设备上要使用的闲鱼应用版本 ")])],1),t("el-form-item",{attrs:{label:"搜索关键词"}},[t("el-input",{attrs:{placeholder:"请输入搜索关键词",maxlength:"50","show-word-limit":""},on:{input:e.onInputChange,blur:e.onInputChange},model:{value:e.config.keyword,callback:function(t){e.$set(e.config,"keyword",t)},expression:"config.keyword"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 输入要搜索的商品关键词，如：手机、电脑、相机等 ")])],1),t("el-form-item",{attrs:{label:"私信内容"}},[t("el-input",{attrs:{type:"textarea",rows:3,placeholder:"请输入私信内容",maxlength:"200","show-word-limit":""},on:{input:e.onInputChange,blur:e.onInputChange},model:{value:e.config.message,callback:function(t){e.$set(e.config,"message",t)},expression:"config.message"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 发送给卖家的私信内容，建议简洁明了 ")])],1),t("el-form-item",{attrs:{label:"目标私信数量"}},[t("el-input-number",{attrs:{min:1,max:100,placeholder:"数量"},on:{change:e.onInputChange},model:{value:e.config.targetCount,callback:function(t){e.$set(e.config,"targetCount",t)},expression:"config.targetCount"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("本次执行要私信的卖家数量")])],1),t("el-form-item",{attrs:{label:"调试模式"}},[t("el-switch",{attrs:{"active-text":"开启","inactive-text":"关闭"},on:{change:e.onInputChange},model:{value:e.config.debugMode,callback:function(t){e.$set(e.config,"debugMode",t)},expression:"config.debugMode"}}),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" 开启调试模式时不会真正发送消息，仅模拟执行过程 ")])],1),t("el-form-item",{attrs:{label:"操作间隔"}},[t("el-input-number",{attrs:{min:1,max:10,placeholder:"秒"},on:{change:e.onInputChange},model:{value:e.config.operationDelay,callback:function(t){e.$set(e.config,"operationDelay",t)},expression:"config.operationDelay"}}),t("span",{staticStyle:{"margin-left":"10px",color:"#909399"}},[e._v("每个操作步骤间隔时间（秒）")])],1),t("el-form-item",{attrs:{label:"安全设置"}},[t("el-checkbox-group",{model:{value:e.config.safetyOptions,callback:function(t){e.$set(e.config,"safetyOptions",t)},expression:"config.safetyOptions"}},[t("el-checkbox",{attrs:{label:"skipChatted"}},[e._v("跳过已私信的帖子")]),t("el-checkbox",{attrs:{label:"saveProgress"}},[e._v("保存执行进度")]),t("el-checkbox",{attrs:{label:"autoStop"}},[e._v("异常时自动停止")])],1),t("div",{staticStyle:{"margin-top":"5px",color:"#909399","font-size":"12px"}},[e._v(" • 跳过已私信的帖子：避免重复私信同一个卖家"),t("br"),e._v(" • 保存执行进度：记录已私信的帖子，下次执行时跳过"),t("br"),e._v(" • 异常时自动停止：遇到错误时自动停止执行 ")])],1),t("el-alert",{attrs:{title:"使用提醒",type:"info",closable:!1,"show-icon":""}},[t("div",[e._v(" • 请先选择对应的闲鱼应用版本，确保应用已安装并可正常使用"),t("br"),e._v(" • 私信内容请遵守平台规则，避免发送广告或违规内容"),t("br"),e._v(" • 建议设置合理的私信数量，避免频繁操作被限制"),t("br"),e._v(" • 执行过程中请勿手动操作手机"),t("br"),e._v(" • 如果应用启动失败，请检查应用版本选择是否正确 ")])]),t("el-form-item",{attrs:{label:"脚本控制"}},[t("el-button",{attrs:{type:"danger",size:"small",disabled:!e.isScriptRunning},on:{click:e.stopScript}},[e._v(" 停止脚本 ")]),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"small",icon:"el-icon-refresh-right",title:"检查并同步脚本执行状态"},on:{click:e.refreshScriptState}},[e._v(" 刷新状态 ")]),e.isScriptRunning?t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"warning",size:"small",icon:"el-icon-refresh",title:"如果脚本状态显示异常，点击此按钮重置状态"},on:{click:e.resetScriptStateManually}},[e._v(" 重置状态 ")]):e._e(),t("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"info",size:"small",icon:"el-icon-chat-dot-round"},on:{click:e.openChatRecordsManager}},[e._v(" 管理私聊记录 ")]),e.isScriptRunning?t("span",{staticStyle:{"margin-left":"10px",color:"#67C23A","font-size":"12px"}},[e._v(" 脚本正在执行中... ")]):e.isScriptCompleted?t("span",{staticStyle:{"margin-left":"10px",color:"#409EFF","font-size":"12px"}},[e._v(" 脚本执行完成，1分钟后可重新执行 ")]):t("span",{staticStyle:{"margin-left":"10px",color:"#909399","font-size":"12px"}},[e._v(" 脚本未在运行 ")])],1)],1),e.isScriptRunning||e.isScriptCompleted||e.currentTaskId?t("el-card",{staticClass:"status-card"},[t("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[t("span",[e._v("实时执行状态")]),t("el-tag",{staticStyle:{float:"right"},attrs:{type:e.isScriptRunning?"warning":e.isScriptCompleted?"success":"info",size:"small"}},[e._v(" "+e._s(e.isScriptRunning?"执行中":e.isScriptCompleted?"已完成":"等待开始")+" ")])],1),t("div",{staticClass:"status-content"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:6}},[t("div",{staticClass:"status-item"},[t("div",{staticClass:"status-label"},[e._v("已私信数量")]),t("div",{staticClass:"status-value"},[e._v(e._s(e.successCount))])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"status-item"},[t("div",{staticClass:"status-label"},[e._v("失败数量")]),t("div",{staticClass:"status-value"},[e._v(e._s(e.failedCount))])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"status-item"},[t("div",{staticClass:"status-label"},[e._v("已处理步骤")]),t("div",{staticClass:"status-value"},[e._v(e._s(e.processedStepCount))])])]),t("el-col",{attrs:{span:6}},[t("div",{staticClass:"status-item"},[t("div",{staticClass:"status-label"},[e._v("搜索尝试次数")]),t("div",{staticClass:"status-value"},[e._v(e._s(e.searchAttemptCount))])])])],1),t("div",{staticClass:"current-status"},[t("div",{staticClass:"status-label"},[e._v("当前状态")]),t("div",{staticClass:"status-text"},[e._v(e._s(e.currentStatus))])]),e.isScriptRunning?t("div",{staticClass:"progress-bar"},[t("el-progress",{attrs:{percentage:e.getProgressPercentage(),status:e.isScriptCompleted?"success":"active","stroke-width":8}})],1):e._e()],1)]):e._e()],1)},a=[],c={data(){return{xianyuApps:[],loadingApps:!1}},watch:{deviceId:{handler(e){e?(console.log("设备ID变化，加载闲鱼应用信息:",e),this.loadDeviceApps(e)):(this.xianyuApps=[],this.config&&void 0!==this.config.selectedApp&&(this.config.selectedApp=""))},immediate:!0}},methods:{async loadDeviceApps(e){if(e){this.loadingApps=!0;try{console.log("加载设备闲鱼应用信息:",e);const t=await this.$http.get(`/api/device/${e}/apps`);t.data.success?(this.xianyuApps=t.data.data.xianyu||[],console.log("闲鱼应用列表:",this.xianyuApps),this.xianyuApps.length>0&&this.config&&(!this.config.selectedApp||""===this.config.selectedApp)&&(this.config.selectedApp=this.xianyuApps[0].text,console.log("自动选择第一个闲鱼应用:",this.config.selectedApp),this.onInputChange?this.onInputChange():this.updateConfig&&this.updateConfig())):(console.error("加载设备闲鱼应用信息失败:",t.data.message),this.xianyuApps=[])}catch(t){console.error("加载设备闲鱼应用信息异常:",t),this.xianyuApps=[]}finally{this.loadingApps=!1}}else console.log("设备ID为空，跳过加载闲鱼应用信息")},onAppSelectionChange(e){console.log("选择的闲鱼应用:",e),this.config&&(this.config.selectedApp=e,this.onInputChange?this.onInputChange():this.updateConfig&&this.updateConfig())},getSelectedXianyuApp(e){return e&&this.xianyuApps.find(t=>t.text===e)||null},getAppCoordinates(e){const t=this.getSelectedXianyuApp(e);return t?t.bounds:null},getAppPackageName(e){const t=this.getSelectedXianyuApp(e);return t?t.name:"com.taobao.idlefish"},getAppLaunchMethod(e){const t=this.getSelectedXianyuApp(e);return t?t.method:"text"},validateAppSelection(e){if(!e)return{valid:!1,message:"请选择要使用的闲鱼应用版本"};const t=this.getSelectedXianyuApp(e);return t?{valid:!0,message:"应用选择有效",app:t}:{valid:!1,message:"选择的闲鱼应用版本无效"}}}},l={name:"XianyuKeywordMessageConfig",mixins:[c],props:{deviceId:{type:String,required:!0}},data(){return{config:{keyword:"",message:"",targetCount:10,debugMode:!0,operationDelay:2,safetyOptions:["skipChatted","saveProgress","autoStop"],selectedApp:""},isScriptRunning:!1,isScriptCompleted:!1,completionTimer:null,successCount:0,failedCount:0,processedStepCount:0,searchAttemptCount:0,currentStatus:"等待开始",socket:null,currentTaskId:null,currentLogId:null}},computed:{isConfigValid(){return""!==this.config.keyword.trim()&&""!==this.config.message.trim()&&this.config.targetCount>0&&""!==this.config.selectedApp.trim()},getProgressPercentage(){return()=>{if(!this.isScriptRunning&&!this.isScriptCompleted)return 0;if(this.isScriptCompleted)return 100;if(this.config.targetCount>0){const e=Math.min(this.successCount/this.config.targetCount*100,95);return Math.round(e)}return Math.min(5*this.processedStepCount,90)}}},watch:{deviceId:{handler(e,t){e!==t&&(console.log("设备ID变化，重新加载配置组件状态:",e),this.restoreComponentState())},immediate:!1},"$store.state.xianyu.functionStates.keywordMessage.isScriptRunning":{handler(e,t){e!==t&&(console.log("Vuex脚本运行状态变化:",e),this.isScriptRunning=e)},immediate:!1},"$store.state.xianyu.functionStates.keywordMessage.isScriptCompleted":{handler(e,t){e!==t&&(console.log("Vuex脚本完成状态变化:",e),this.isScriptCompleted=e)},immediate:!1}},mounted(){this._mountTime=Date.now(),this._isRouteChange=!0,console.log("[XianyuKeywordMessageConfig] 组件已挂载"),console.log("[XianyuKeywordMessageConfig] 挂载时间记录:",new Date(this._mountTime).toLocaleTimeString()),console.log("[XianyuKeywordMessageConfig] 标记为路由切换挂载，将跳过状态验证"),console.log("[XianyuKeywordMessageConfig] 挂载时的初始状态:",{currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,deviceId:this.deviceId}),setTimeout(()=>{this._isRouteChange=!1,console.log("[XianyuKeywordMessageConfig] 30秒后允许状态验证")},3e4),this.initEventListeners(),this.loadSavedConfig(),console.log("[XianyuKeywordMessageConfig] 开始调用状态恢复方法...");try{this.restoreComponentState().then(()=>{console.log("[XianyuKeywordMessageConfig] 状态恢复完成，开始验证状态一致性"),console.log("[XianyuKeywordMessageConfig] 恢复后的状态:",{currentTaskId:this.currentTaskId,currentLogId:this.currentLogId,deviceId:this.deviceId,isScriptRunning:this.isScriptRunning}),setTimeout(()=>{if(this.isScriptRunning)return console.log("[XianyuKeywordMessageConfig] 检测到脚本运行状态，立即验证状态一致性"),void this.validateStateConsistency();this._isRouteChange?console.log("[XianyuKeywordMessageConfig] 检测到路由切换挂载，跳过状态验证"):this._mountTime&&Date.now()-this._mountTime>15e3?(console.log("[XianyuKeywordMessageConfig] 组件挂载超过15秒，开始状态验证"),this.validateStateConsistency()):(console.log("[XianyuKeywordMessageConfig] 组件挂载时间不足15秒，跳过状态验证"),console.log("[XianyuKeywordMessageConfig] 挂载时间:",this._mountTime?Date.now()-this._mountTime+"ms":"未记录"))},3e3),this.initializeSocket()}).catch(e=>{console.error("[XianyuKeywordMessageConfig] 状态恢复失败:",e),console.error("[XianyuKeywordMessageConfig] 错误堆栈:",e.stack),this.initializeSocket()})}catch(e){console.error("[XianyuKeywordMessageConfig] 调用状态恢复方法时发生同步错误:",e),console.error("[XianyuKeywordMessageConfig] 同步错误堆栈:",e.stack),this.initializeSocket()}this.$nextTick(()=>{this.onInputChange()})},beforeDestroy(){console.log("🔧 [XianyuKeywordMessageConfig] 组件即将销毁，执行清理操作"),this.saveComponentState(),console.log("🔧 [XianyuKeywordMessageConfig] 保持WebSocket连接，仅清理事件监听"),this.cleanupEventListeners(),this.completionTimer&&clearTimeout(this.completionTimer),console.log("🔧 [XianyuKeywordMessageConfig] 组件清理完成")},methods:{initEventListeners(){this.$root.$on("xianyu-task-started",this.handleTaskStarted),this.$root.$on("xianyu-task-stopped",this.handleTaskStopped),this.$root.$on("xianyu-task-completed",this.handleTaskCompleted),this.$root.$on("device-offline",this.handleDeviceOffline),this.$root.$on("device-status-updated",this.handleDeviceStatusUpdated),this.$root.$on("xianyu-restore-state",this.handleStateRestore),this.$root.$on("xianyu-reset-all-states",this.handleGlobalResetStates)},cleanupEventListeners(){this.$root.$off("xianyu-task-started",this.handleTaskStarted),this.$root.$off("xianyu-task-stopped",this.handleTaskStopped),this.$root.$off("xianyu-task-completed",this.handleTaskCompleted),this.$root.$off("device-offline",this.handleDeviceOffline),this.$root.$off("device-status-updated",this.handleDeviceStatusUpdated),this.$root.$off("xianyu-restore-state",this.handleStateRestore),this.$root.$off("xianyu-reset-all-states",this.handleGlobalResetStates)},handleTaskStarted(e){"keywordMessage"===e.functionType&&e.deviceId===this.deviceId&&(this.isScriptRunning=!0,this.isScriptCompleted=!1,this.currentTaskId=e.taskId||Date.now().toString(),console.log("闲鱼关键词私信脚本开始执行:",e),console.log("设置currentTaskId:",this.currentTaskId),this.$store.dispatch("xianyu/taskStarted",{functionType:"keywordMessage",taskId:e.taskId||Date.now().toString(),logId:e.logId||Date.now().toString(),deviceId:this.deviceId,config:e.config||this.config}),this.saveComponentState())},handleTaskStopped(e){if(console.log("[XianyuKeywordMessageConfig] 收到任务停止事件:",e),console.log("[XianyuKeywordMessageConfig] 当前设备ID:",this.deviceId),console.log("[XianyuKeywordMessageConfig] 当前脚本运行状态:",this.isScriptRunning),e.deviceId===this.deviceId){console.log("[XianyuKeywordMessageConfig] 设备ID匹配，处理任务停止事件");const t=!e.functionType||"keywordMessage"===e.functionType||"keyword_message"===e.functionType||e.functionType.includes("keyword")||e.functionType.includes("Message");console.log("[XianyuKeywordMessageConfig] 功能类型匹配检查:",{dataFunctionType:e.functionType,matches:t}),t?(console.log("[XianyuKeywordMessageConfig] 功能类型匹配，重置脚本状态"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentTaskId=null,this.currentLogId=null,this.successCount=0,this.failedCount=0,this.processedStepCount=0,this.searchAttemptCount=0,this.currentStatus="等待开始",console.log("闲鱼关键词私信脚本已停止:",e),this.$store.dispatch("xianyu/clearDeviceExecutionState",this.deviceId),this.saveComponentState(),console.log("[XianyuKeywordMessageConfig] 脚本状态重置完成")):console.log("[XianyuKeywordMessageConfig] 功能类型不匹配，忽略停止事件")}else console.log("[XianyuKeywordMessageConfig] 设备ID不匹配，忽略停止事件")},handleTaskCompleted(e){if(console.log("[XianyuKeywordMessageConfig] 收到任务完成事件:",e),console.log("[XianyuKeywordMessageConfig] 当前设备ID:",this.deviceId),e.deviceId===this.deviceId){console.log("[XianyuKeywordMessageConfig] 设备ID匹配，处理任务完成事件");const t=!e.functionType||"keywordMessage"===e.functionType||"keyword_message"===e.functionType||e.functionType.includes("keyword")||e.functionType.includes("Message");console.log("[XianyuKeywordMessageConfig] 功能类型匹配检查:",{dataFunctionType:e.functionType,matches:t}),t?(console.log("[XianyuKeywordMessageConfig] 功能类型匹配，设置脚本完成状态"),this.isScriptRunning=!1,this.isScriptCompleted=!0,this.currentTaskId=null,this.currentLogId=null,console.log("闲鱼关键词私信脚本执行完成:",e),this.$store.dispatch("xianyu/clearDeviceExecutionState",this.deviceId),this.saveComponentState(),this.completionTimer=setTimeout(()=>{this.isScriptCompleted=!1,this.saveComponentState(),console.log("[XianyuKeywordMessageConfig] 完成状态已重置")},6e4),console.log("[XianyuKeywordMessageConfig] 脚本完成状态设置完成")):console.log("[XianyuKeywordMessageConfig] 功能类型不匹配，忽略完成事件")}else console.log("[XianyuKeywordMessageConfig] 设备ID不匹配，忽略完成事件")},handleDeviceOffline(e){e.deviceId===this.deviceId&&(console.log("设备离线，重置闲鱼配置组件状态:",e),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.completionTimer&&(clearTimeout(this.completionTimer),this.completionTimer=null),this.$store.dispatch("xianyu/clearDeviceExecutionState",this.deviceId),this.saveComponentState())},handleDeviceStatusUpdated(e){e.deviceId===this.deviceId&&"online"===e.status&&(console.log("设备状态更新为在线，检查是否需要重置脚本状态:",e),this.isScriptRunning&&(console.log("设备变为在线，重置脚本执行状态"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentTaskId=null,this.$store.dispatch("xianyu/clearDeviceExecutionState",this.deviceId),this.saveComponentState()))},handleStateRestore(){console.log("闲鱼配置组件: 收到状态恢复事件"),this.restoreComponentState()},handleGlobalResetStates(){console.log("闲鱼配置组件: 收到全局重置状态事件"),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.completionTimer&&(clearTimeout(this.completionTimer),this.completionTimer=null),this.resetConfigToDefault(),console.log("闲鱼配置组件状态已重置")},onInputChange(){this.$emit("config-change",{deviceId:this.deviceId,config:{...this.config},isValid:this.isConfigValid}),this.saveConfig()},onAppSelectionChange(){console.log("闲鱼应用选择变化:",this.config.selectedApp);const e=this.validateAppSelection(this.config.selectedApp);e.valid?(console.log("选择的闲鱼应用信息:",e.app),this.onInputChange()):this.$message.warning(e.message)},stopScript(){this.isScriptRunning&&(this.$emit("stop-script",{deviceId:this.deviceId,functionType:"keywordMessage"}),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.$store.dispatch("xianyu/clearDeviceExecutionState",this.deviceId),this.saveComponentState())},async refreshScriptState(){if(console.log("[XianyuKeywordMessageConfig] 用户手动刷新脚本状态"),this.deviceId)try{this.$message.info("正在检查脚本执行状态..."),await this.fallbackStateValidation(),this.$message.success("脚本状态已刷新")}catch(e){console.error("[XianyuKeywordMessageConfig] 刷新脚本状态失败:",e),this.$message.error("刷新状态失败: "+(e.message||"未知错误"))}else this.$message.warning("请先选择设备")},async resetScriptStateManually(){try{const e=await this.$confirm("确定要重置脚本状态吗？这将清除当前的执行状态信息。","重置脚本状态",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});e&&(console.log("[XianyuKeywordMessageConfig] 用户手动重置脚本状态"),this.resetScriptState("用户手动重置"),this.$message.success("脚本状态已重置"))}catch(e){console.log("[XianyuKeywordMessageConfig] 用户取消重置操作")}},openChatRecordsManager(){console.log("打开私聊记录管理页面，设备ID:",this.deviceId),this.$router.push({name:"XianyuChatRecords",params:{deviceId:this.deviceId},query:{deviceName:`设备_${this.deviceId}`,from:"xianyu-automation"}})},saveConfig(){try{const e=`xianyu_keyword_message_config_${this.deviceId}`;localStorage.setItem(e,JSON.stringify(this.config))}catch(e){console.warn("保存配置失败:",e)}},loadSavedConfig(){try{const e=`xianyu_keyword_message_config_${this.deviceId}`,t=localStorage.getItem(e);if(t){const e=JSON.parse(t);this.config={...this.config,...e},this.onInputChange()}}catch(e){console.warn("加载配置失败:",e)}},getConfig(){return{...this.config}},setConfig(e){e&&(this.config={keyword:e.keyword||"",message:e.message||"",targetCount:e.targetCount||10,debugMode:!1!==e.debugMode,operationDelay:e.operationDelay||2,safetyOptions:e.safetyOptions||["skipChatted","saveProgress","autoStop"]},this.$forceUpdate(),this.$nextTick(()=>{this.onInputChange()}),console.log("设置闲鱼配置参数:",e),console.log("当前配置状态:",this.config))},resetConfig(){this.config={keyword:"",message:"",targetCount:10,debugMode:!0,operationDelay:2,safetyOptions:["skipChatted","saveProgress","autoStop"]},this.onInputChange()},resetConfigToDefault(){console.log("重置闲鱼配置参数到默认值"),this.config.keyword="",this.config.message="",this.config.targetCount=10,this.config.debugMode=!0,this.config.operationDelay=2,this.config.safetyOptions=["skipChatted","saveProgress","autoStop"];const e=`xianyu_keyword_message_config_${this.deviceId}`;localStorage.removeItem(e),this.onInputChange(),console.log("闲鱼配置参数已重置完成")},async saveComponentState(){try{await this.$store.dispatch("xianyu/setFunctionState",{functionType:"keywordMessage",stateData:{isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,config:this.config}}),console.log("闲鱼配置组件状态已保存到Vuex")}catch(e){console.error("保存闲鱼配置组件状态失败:",e)}},async initializeSocket(){try{const{getWebSocketManager:e}=await Promise.resolve().then(s.bind(s,6006));this.wsManager=e(),console.log("🔧 [XianyuKeywordMessageConfig] 开始初始化WebSocket连接"),await this.wsManager.init(),this.socket=this.wsManager.socket,console.log("✅ [XianyuKeywordMessageConfig] WebSocket连接成功"),this.socket.onAny((e,t)=>{(e.includes("xianyu")||e.includes("test"))&&console.log(`🔍 [XianyuKeywordMessageConfig] 收到WebSocket事件: ${e}`,t)}),this.socket.on("xianyu_task_started",e=>{console.log("[XianyuKeywordMessageConfig] 收到WebSocket任务开始事件:",e),this.handleTaskStarted(e)}),this.socket.on("xianyu_task_stopped",e=>{console.log("[XianyuKeywordMessageConfig] 收到WebSocket任务停止事件:",e),this.handleTaskStopped(e)}),this.socket.on("xianyu_task_completed",e=>{console.log("[XianyuKeywordMessageConfig] 收到WebSocket任务完成事件:",e),this.handleTaskCompleted(e)}),this.socket.on("xianyu_execution_completed",e=>{console.log("[XianyuKeywordMessageConfig] 收到WebSocket脚本执行完成事件:",e),e.deviceId!==this.deviceId&&this.deviceId||(console.log("[XianyuKeywordMessageConfig] 脚本执行完成，更新状态"),this.isScriptRunning=!1,this.isScriptCompleted="success"===e.status,"success"===e.status&&setTimeout(()=>{this.isScriptCompleted=!1},6e4),this.saveComponentState())}),this.socket.on("xianyu_realtime_status",e=>{console.log("🎯 [XianyuKeywordMessageConfig] 收到WebSocket实时状态事件:",e),this.handleRealtimeStatus(e)}),this.socket.on("test_realtime_broadcast",e=>{console.log("🧪 [XianyuKeywordMessageConfig] 收到测试广播:",e)}),console.log("✅ [XianyuKeywordMessageConfig] Socket初始化完成")}catch(e){console.error("❌ [XianyuKeywordMessageConfig] Socket初始化失败:",e)}},handleRealtimeStatus(e){console.log("🔄 [XianyuKeywordMessageConfig] 收到实时状态数据:",e),console.log("📋 [XianyuKeywordMessageConfig] 当前组件taskId:",this.currentTaskId),console.log("📋 [XianyuKeywordMessageConfig] 数据中的taskId:",e.taskId),console.log("🔍 [XianyuKeywordMessageConfig] taskId匹配:",this.currentTaskId&&e.taskId===this.currentTaskId),!this.currentTaskId&&e.taskId&&e.taskId.includes("xianyu")&&(console.log("🔄 [XianyuKeywordMessageConfig] 检测到taskId为空，尝试从WebSocket数据恢复taskId:",e.taskId),this.currentTaskId=e.taskId,console.log("✅ [XianyuKeywordMessageConfig] 已从WebSocket数据恢复taskId:",this.currentTaskId),this.saveComponentState()),this.currentTaskId&&e.taskId===this.currentTaskId?(console.log("✅ [XianyuKeywordMessageConfig] taskId匹配，更新实时状态:",e),void 0!==e.successCount&&(this.successCount=e.successCount,console.log("📊 [XianyuKeywordMessageConfig] 更新成功私信数:",this.successCount)),void 0!==e.failedCount&&(this.failedCount=e.failedCount,console.log("📊 [XianyuKeywordMessageConfig] 更新失败私信数:",this.failedCount)),void 0!==e.processedStepCount&&(this.processedStepCount=e.processedStepCount,console.log("📊 [XianyuKeywordMessageConfig] 更新已处理步骤数:",this.processedStepCount)),void 0!==e.searchAttemptCount&&(this.searchAttemptCount=e.searchAttemptCount,console.log("📊 [XianyuKeywordMessageConfig] 更新搜索尝试次数:",this.searchAttemptCount)),e.currentStatus&&(this.currentStatus=e.currentStatus,console.log("📊 [XianyuKeywordMessageConfig] 更新当前状态:",this.currentStatus)),console.log("✅ [XianyuKeywordMessageConfig] 实时状态已更新:",{successCount:this.successCount,failedCount:this.failedCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}),this.$forceUpdate(),console.log("🔄 [XianyuKeywordMessageConfig] 已强制更新视图")):console.log("❌ [XianyuKeywordMessageConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新")},async saveComponentState(){const{saveComponentState:e}=await Promise.resolve().then(s.bind(s,5596)),t={config:this.config,currentLogId:this.currentLogId,currentTaskId:this.currentTaskId,isScriptRunning:this.isScriptRunning,isScriptCompleted:this.isScriptCompleted,realtimeData:{successCount:this.successCount,failedCount:this.failedCount,processedStepCount:this.processedStepCount,searchAttemptCount:this.searchAttemptCount,currentStatus:this.currentStatus}};await e(this,"xianyuKeywordMessage",t),console.log("[XianyuKeywordMessageConfig] 组件状态已保存，currentTaskId:",this.currentTaskId)},async validateStateConsistency(){console.log("🔍 [XianyuKeywordMessageConfig] 开始验证状态一致性"),this.deviceId?(console.log("[XianyuKeywordMessageConfig] 使用备选验证方案（避免API问题）"),await this.fallbackStateValidation()):console.log("[XianyuKeywordMessageConfig] 设备ID为空，跳过状态验证")},async fallbackStateValidation(){try{if(console.log("[XianyuKeywordMessageConfig] 使用备选方案验证状态"),this._isRouteChange&&!this.isScriptRunning)return void console.log("[XianyuKeywordMessageConfig] 检测到路由切换挂载且脚本未运行，跳过备用状态验证");console.log("[XianyuKeywordMessageConfig] 当前脚本状态:",{isScriptRunning:this.isScriptRunning,currentTaskId:this.currentTaskId,deviceId:this.deviceId});const t=await this.$http.get("/api/xianyu/logs",{params:{page:1,limit:5,deviceId:this.deviceId,executionStatus:"running"}}),s=t.data.data.logs||[];if(console.log("[XianyuKeywordMessageConfig] 查询到的正在执行任务:",s.length,"个"),console.log("[XianyuKeywordMessageConfig] 查询响应详情:",t.data),s.length>0&&console.log("[XianyuKeywordMessageConfig] 正在执行的任务详情:",s),this.isScriptRunning&&0===s.length){console.log("[XianyuKeywordMessageConfig] 前端显示脚本运行中，但实际没有运行任务"),this._isRouteChange&&console.log("[XianyuKeywordMessageConfig] 检测到路由切换挂载，但脚本状态不一致，继续验证");const t=Date.now()-(this._mountTime||Date.now());if(t<5e3)return console.log("[XianyuKeywordMessageConfig] 组件挂载时间不足5秒，跳过自动重置（等待初始化完成）"),console.log("[XianyuKeywordMessageConfig] 挂载时间:",t+"ms"),void console.log("[XianyuKeywordMessageConfig] 当前时间:",(new Date).toLocaleTimeString());try{const e=await this.$http.get("/api/device/list"),t=e.data.data||[],s=t.find(e=>e.device_id===this.deviceId);if(s&&"busy"===s.status)return void console.log("[XianyuKeywordMessageConfig] 设备状态为busy，可能有任务在执行，跳过重置")}catch(e){return void console.log("[XianyuKeywordMessageConfig] 检查设备状态失败，跳过重置:",e.message)}console.log("[XianyuKeywordMessageConfig] 确认需要重置状态"),console.log("[XianyuKeywordMessageConfig] 清理localStorage中的错误状态");const s=`xianyuKeywordMessage_backup_${this.deviceId}`;localStorage.removeItem(s),this.resetScriptState("实际没有运行任务"),this.$message.warning("检测到脚本状态异常，已自动重置状态")}else!this.isScriptRunning&&s.length>0?(console.log("[XianyuKeywordMessageConfig] 前端显示脚本未运行，但实际有运行任务，恢复状态"),console.log("[XianyuKeywordMessageConfig] 即将调用restoreFromRunningTask方法"),console.log("[XianyuKeywordMessageConfig] 正在执行的任务详情:",s),await this.restoreFromRunningTask()):this.isScriptRunning&&s.length>0?console.log("[XianyuKeywordMessageConfig] 状态一致，脚本正在运行"):this.isScriptRunning||0!==s.length||console.log("[XianyuKeywordMessageConfig] 状态一致，脚本未运行")}catch(t){console.error("[XianyuKeywordMessageConfig] 备选状态验证失败:",t),this.isScriptRunning&&(console.log("[XianyuKeywordMessageConfig] 查询失败但前端显示运行中，建议手动重置"),this.$message.warning('无法验证脚本状态，如果脚本未在运行，请点击"重置状态"按钮'))}},async restoreFromRunningTask(){try{const t=await this.$http.get("/api/xianyu/logs",{params:{page:1,limit:1,deviceId:this.deviceId,executionStatus:"running"}}),s=t.data.data.logs||[];if(console.log("[XianyuKeywordMessageConfig] restoreFromRunningTask查询响应详情:",t.data),console.log("[XianyuKeywordMessageConfig] restoreFromRunningTask查询到的任务数量:",s.length),s.length>0&&console.log("[XianyuKeywordMessageConfig] restoreFromRunningTask正在执行的任务详情:",s),s.length>0){const t=s[0];if(console.log("[XianyuKeywordMessageConfig] 恢复脚本执行状态，任务信息:",t),this.isScriptRunning=!0,this.isScriptCompleted=!1,this.currentTaskId=t.id,this.currentLogId=t.id,t.configParams)try{const e="string"===typeof t.configParams?JSON.parse(t.configParams):t.configParams;this.config={...this.config,...e}}catch(e){console.warn("[XianyuKeywordMessageConfig] 解析配置参数失败:",e)}this.$store.dispatch("xianyu/setFunctionState",{functionType:"keywordMessage",stateData:{isScriptRunning:!0,isScriptCompleted:!1,currentTaskId:t.id,currentLogId:t.id,config:this.config}}),this.saveComponentState(),this.$message.success("检测到正在执行的任务，已自动恢复脚本状态")}}catch(t){console.error("[XianyuKeywordMessageConfig] 从正在执行的任务中恢复状态失败:",t)}},resetScriptState(e){console.log(`[XianyuKeywordMessageConfig] 重置脚本状态，原因: ${e}`),this.isScriptRunning=!1,this.isScriptCompleted=!1,this.currentTaskId=null,this.currentLogId=null,this.successCount=0,this.failedCount=0,this.processedStepCount=0,this.searchAttemptCount=0,this.currentStatus="等待开始",this.$store.dispatch("xianyu/clearDeviceExecutionState",this.deviceId),this.saveComponentState(),console.log("[XianyuKeywordMessageConfig] 脚本状态已重置")},async restoreComponentState(){console.log("🚀 [XianyuKeywordMessageConfig] restoreComponentState方法被调用");try{console.log("[XianyuKeywordMessageConfig] 开始恢复组件状态..."),console.log("[XianyuKeywordMessageConfig] 当前deviceId:",this.deviceId);const e=this.$store.getters["xianyu/getFunctionState"]("keywordMessage");console.log("[XianyuKeywordMessageConfig] Vuex中的状态:",e);const t=`xianyuKeywordMessage_backup_${this.deviceId||"default"}`,i=localStorage.getItem(t);console.log("[XianyuKeywordMessageConfig] localStorage备份键:",t),console.log("[XianyuKeywordMessageConfig] localStorage备份数据:",i);const{restoreComponentState:o}=await Promise.resolve().then(s.bind(s,5596)),n=await o(this,"xianyuKeywordMessage");console.log("[XianyuKeywordMessageConfig] 状态管理工具返回的状态:",n),n&&Object.keys(n).length>0?(console.log("[XianyuKeywordMessageConfig] 恢复组件状态:",n),n.config&&Object.keys(n.config).length>0&&(this.config={...this.config,...n.config}),this.currentLogId=n.currentLogId||null,this.currentTaskId=n.currentTaskId||null,this.isScriptRunning=n.isScriptRunning||!1,this.isScriptCompleted=n.isScriptCompleted||!1,n.realtimeData&&(this.successCount=n.realtimeData.successCount||0,this.failedCount=n.realtimeData.failedCount||0,this.processedStepCount=n.realtimeData.processedStepCount||0,this.searchAttemptCount=n.realtimeData.searchAttemptCount||0,this.currentStatus=n.realtimeData.currentStatus||"等待开始",console.log("[XianyuKeywordMessageConfig] 实时状态已恢复:",n.realtimeData)),console.log("[XianyuKeywordMessageConfig] 组件状态已恢复，currentTaskId:",this.currentTaskId),this.isScriptRunning&&console.log("[XianyuKeywordMessageConfig] 从状态恢复中发现脚本正在运行，将进行状态验证")):(console.log("[XianyuKeywordMessageConfig] 没有找到保存的状态，检查是否有正在执行的任务"),await this.tryRestoreFromRunningTasks())}catch(e){console.error("[XianyuKeywordMessageConfig] 恢复组件状态失败:",e)}},async tryRestoreFromRunningTasks(){if(this.deviceId)try{console.log("[XianyuKeywordMessageConfig] 尝试从正在执行的任务中恢复状态");const e=await this.$http.get("/api/xianyu/logs",{params:{page:1,limit:1,deviceId:this.deviceId,executionStatus:"running"}}),t=e.data.data.logs||[];if(console.log(`[XianyuKeywordMessageConfig] 找到 ${t.length} 个正在执行的任务`),console.log("[XianyuKeywordMessageConfig] tryRestoreFromRunningTasks查询响应详情:",e.data),!(t.length>0)){if(console.log("[XianyuKeywordMessageConfig] tryRestoreFromRunningTasks未找到正在执行的任务，清理可能的错误状态"),this.isScriptRunning){console.log("[XianyuKeywordMessageConfig] 强制清理错误的脚本运行状态");const e=`xianyuKeywordMessage_backup_${this.deviceId}`;localStorage.removeItem(e),this.resetScriptState("数据库中无正在执行的任务")}return}if(console.log("[XianyuKeywordMessageConfig] tryRestoreFromRunningTasks正在执行的任务详情:",t),t.length>0){const e=t[0];console.log("[XianyuKeywordMessageConfig] 从正在执行的任务中恢复状态:",e),this.isScriptRunning=!0,this.isScriptCompleted=!1,this.currentTaskId=e.id,this.currentLogId=e.id,e.configParams&&(this.config={...this.config,...e.configParams},console.log("[XianyuKeywordMessageConfig] 已恢复配置参数:",e.configParams)),this.$store.dispatch("xianyu/setFunctionState",{functionType:"keywordMessage",stateData:{isScriptRunning:!0,isScriptCompleted:!1,currentTaskId:e.id,currentLogId:e.id,config:this.config}}),this.saveComponentState(),console.log("[XianyuKeywordMessageConfig] 已从正在执行的任务中恢复脚本状态")}else console.log("[XianyuKeywordMessageConfig] 没有找到正在执行的任务"),this.currentLogId=null,this.currentTaskId=null,this.isScriptRunning=!1,this.isScriptCompleted=!1}catch(e){console.error("[XianyuKeywordMessageConfig] 从正在执行的任务中恢复状态失败:",e),this.currentLogId=null,this.currentTaskId=null,this.isScriptRunning=!1,this.isScriptCompleted=!1}else console.log("[XianyuKeywordMessageConfig] 设备ID为空，无法从正在执行的任务中恢复")}}},r=l,d=s(1656),g=(0,d.A)(r,n,a,!1,null,"4cd39312",null),u=g.exports,h=s(9534),p={name:"XianyuAutomation",components:{XianyuKeywordMessageConfig:u,DeviceInfo:h.A},data(){return{selectedFunction:"",selectedDevices:[],executing:!1,deviceConfigs:{},functions:[{key:"keywordMessage",name:"关键词私信",description:"搜索关键词商品并自动私信卖家",icon:"el-icon-chat-dot-round"}],activeDeviceTab:"",refreshTimer:null}},computed:{devices(){return this.$store.getters["device/devices"]||[]},allDevices(){return this.$store.getters["device/devices"]||[]},onlineDevices(){return this.allDevices.filter(e=>"online"===e.status||"busy"===e.status)},connectedDevicesCount(){return this.onlineDevices.length},onlineDevicesCount(){return this.onlineDevices.filter(e=>"online"===e.status).length},busyDevicesCount(){return this.onlineDevices.filter(e=>"busy"===e.status).length},canExecute(){return this.selectedDevices.length>0&&this.selectedFunction&&!this.executing&&this.hasValidConfigs()}},async mounted(){console.log("闲鱼自动化页面: mounted生命周期开始");const e=this.$store.getters["auth/isAuthenticated"];console.log("认证状态:",e),e||(console.warn("用户未登录，这可能导致设备选择没反应"),this.$message.warning("请先登录以使用设备选择功能")),await this.loadDevices(),this.initSocketListeners(),await this.restoreExecutionState(),await new Promise(e=>setTimeout(e,500)),await this.restorePageState(),this.startAutoRefresh(),this.$root.$on("xianyu-task-stopped",this.handleGlobalTaskStopped),this.$root.$on("xianyu-reset-all-states",this.handleGlobalResetStates)},beforeDestroy(){console.log("🔧 [XianyuAutomation] 组件即将销毁，执行清理操作"),this.savePageState(),this.cleanupSocketListeners(),this.stopAutoRefresh(),this.$root.$off("xianyu-task-stopped",this.handleGlobalTaskStopped),this.$root.$off("xianyu-reset-all-states",this.handleGlobalResetStates),console.log("🔧 [XianyuAutomation] 组件清理完成，保持WebSocket连接")},methods:{async loadDevices(){try{console.log("闲鱼自动化: 开始加载设备列表"),await this.$store.dispatch("device/fetchDevices");const e=this.$store.getters["device/devices"];console.log("闲鱼自动化: 设备列表加载完成",e.length,"个设备");const t=e.filter(e=>"online"===e.status),s=e.filter(e=>"busy"===e.status),i=e.filter(e=>"online"===e.status||"busy"===e.status);console.log("设备状态统计:",{total:e.length,online:t.length,busy:s.length,available:i.length}),0===i.length&&console.warn("没有可用设备，这可能导致设备选择没反应")}catch(e){console.error("闲鱼自动化: 加载设备列表失败",e)}},selectFunction(e){console.log("闲鱼自动化: 选择功能:",e),this.selectedFunction=e,this.selectedDevices=[],this.deviceConfigs={},this.activeDeviceTab="",this.$nextTick(async()=>{this.allDevices&&0!==this.allDevices.length||(console.log("闲鱼自动化: 设备列表未加载，重新加载..."),await this.loadDevices()),await new Promise(e=>setTimeout(e,200)),this.autoRestoreExecutingDevices()}),this.savePageState()},getCurrentFunctionName(){const e=this.functions.find(e=>e.key===this.selectedFunction);return e?e.name:"未选择功能"},getComponentName(){const e={keywordMessage:"XianyuKeywordMessageConfig"};return e[this.selectedFunction]||null},getDeviceName(e){if(!this.allDevices||!Array.isArray(this.allDevices)||0===this.allDevices.length)return e;const t=this.allDevices.find(t=>t.device_id===e);return t&&t.device_name||e},getDeviceNameWithStatus(e){if(!this.allDevices||!Array.isArray(this.allDevices)||0===this.allDevices.length)return e;const t=this.allDevices.find(t=>t.device_id===e);if(!t)return e;const s=t.device_name||e,i="busy"===t.status?"忙碌":"在线";return`${s} (${i})`},getDeviceTagType(e){if(!this.allDevices||!Array.isArray(this.allDevices)||0===this.allDevices.length)return"info";const t=this.allDevices.find(t=>t.device_id===e);return t?"busy"===t.status?"warning":"success":"info"},isDeviceExecutingCurrentFunction(e){if(!this.selectedFunction)return!1;console.log(`闲鱼自动化: 检查设备 ${e} 是否正在执行功能 ${this.selectedFunction}`);const t=this.$store.getters["xianyu/getDeviceTasks"](e);if(console.log(`闲鱼自动化: 设备 ${e} 的任务列表:`,t),!t||0===t.length)return console.log(`闲鱼自动化: 设备 ${e} 没有任务`),!1;const s=t.some(e=>{const t=e.functionType===this.selectedFunction&&"running"===e.status;return console.log("闲鱼自动化: 任务检查:",{taskFunctionType:e.functionType,currentFunction:this.selectedFunction,taskStatus:e.status,matches:t}),t});return console.log(`闲鱼自动化: 设备 ${e} 是否正在执行当前功能:`,s),s},autoRestoreExecutingDevices(){if(!this.selectedFunction)return void console.log("闲鱼自动化: 没有选择功能，跳过自动恢复");console.log("闲鱼自动化: 检查是否有正在执行当前功能的设备"),console.log("闲鱼自动化: 当前功能:",this.selectedFunction);const e=this.allDevices&&this.allDevices.length>0?this.allDevices:this.$store.getters["device/devices"];console.log("闲鱼自动化: 设备列表:",e),console.log("闲鱼自动化: 当前Vuex状态:",{currentTasks:this.$store.getters["xianyu/getCurrentTasks"],functionStates:this.$store.state.xianyu.functionStates});const t=e.filter(e=>{const t="busy"===e.status;let s=!1;const i=this.$store.getters["xianyu/getDeviceTasks"](e.device_id);if(i&&i.length>0&&(s=i.some(e=>e.functionType===this.selectedFunction&&"running"===e.status)),!s&&t){const t=this.$store.state.xianyu.functionStates[this.selectedFunction];if(t&&t.isScriptRunning){const t=this.$store.getters["xianyu/getCurrentTasks"];s=!!t[e.device_id]}}return console.log(`闲鱼自动化: 设备 ${e.device_name} 检查:`,{status:e.status,isBusy:t,isExecutingCurrentFunction:s,deviceId:e.device_id,currentFunction:this.selectedFunction,deviceTasks:i}),t&&s});t.length>0?(console.log("闲鱼自动化: 发现正在执行当前功能的设备:",t),t.forEach(e=>{this.selectedDevices.includes(e.device_id)||(this.selectedDevices.push(e.device_id),console.log("闲鱼自动化: 自动选择设备:",e.device_name))}),this.selectedDevices.length>0&&!this.activeDeviceTab&&(this.activeDeviceTab=this.selectedDevices[0]),this.savePageState(),this.$message.success(`已自动选择 ${t.length} 个正在执行的设备`)):console.log("闲鱼自动化: 没有发现正在执行当前功能的设备")},removeDevice(e){const t=this.selectedDevices.indexOf(e);t>-1&&(this.selectedDevices.splice(t,1),this.activeDeviceTab===e&&this.selectedDevices.length>0?this.activeDeviceTab=this.selectedDevices[0]:0===this.selectedDevices.length&&(this.activeDeviceTab=""),this.savePageState())},handleDeviceSelected(e){if(e&&"busy"===e.status){const t=this.isDeviceExecutingCurrentFunction(e.device_id);if(!t)return this.$message.warning(`设备 ${e.device_name} 正在执行其他脚本，无法选择`),void console.log("闲鱼自动化: 尝试选择忙碌设备，已拒绝",e);console.log("闲鱼自动化: 设备正在执行当前功能，允许选择",e)}e&&!this.selectedDevices.includes(e.device_id)&&(this.selectedDevices.push(e.device_id),1===this.selectedDevices.length&&(this.activeDeviceTab=e.device_id),console.log("闲鱼自动化: 已选择设备",e.device_name),this.savePageState())},handleDeviceRemoved(e){console.log("闲鱼自动化: 设备移除事件",e),this.removeDevice(e)},handleDevicesSelectionChanged(e){console.log("闲鱼自动化: 设备选择变化事件",e),this.selectedDevices=[...e],this.selectedDevices.length>0&&!this.activeDeviceTab&&(this.activeDeviceTab=this.selectedDevices[0],console.log("闲鱼自动化: 设置活跃标签",this.activeDeviceTab)),!this.selectedDevices.includes(this.activeDeviceTab)&&this.selectedDevices.length>0?(this.activeDeviceTab=this.selectedDevices[0],console.log("闲鱼自动化: 切换活跃标签",this.activeDeviceTab)):0===this.selectedDevices.length&&(this.activeDeviceTab="",console.log("闲鱼自动化: 清空活跃标签")),console.log("闲鱼自动化: 当前选中设备",this.selectedDevices),this.savePageState()},handleTabClick(e){this.activeDeviceTab=e.name},handleConfigChange(e){this.deviceConfigs[e.deviceId]=e.config,console.log("设备配置更新:",e)},async handleStopScript(e){console.log("停止脚本请求:",e);try{const t=await this.$http.post("/api/xianyu/stop",{deviceId:e.deviceId,taskId:e.taskId||null});t.data.success?(this.$message.success("停止命令已发送"),console.log("闲鱼脚本停止成功:",t.data),this.$store.dispatch("device/clearDeviceExecutionStatus",e.deviceId),console.log(`已清除设备 ${e.deviceId} 的执行状态`),this.$store.dispatch("xianyu/clearDeviceExecutionState",e.deviceId),this.$store.dispatch("device/updateDeviceStatus",{deviceId:e.deviceId,status:"online",lastSeen:new Date}),this.loadDevices(),this.$root.$emit("xianyu-task-stopped",{functionType:e.functionType||"keywordMessage",deviceId:e.deviceId,taskId:e.taskId,message:"任务已停止"})):(this.$message.error("停止失败: "+t.data.message),console.error("闲鱼脚本停止失败:",t.data))}catch(t){console.error("停止闲鱼脚本失败:",t),this.$message.error("停止失败: "+(t.response?.data?.message||t.message))}},isDeviceConfigValid(e){const t=this.deviceConfigs[e];if(!t)return!1;switch(this.selectedFunction){case"keywordMessage":return t.keyword&&""!==t.keyword.trim()&&t.message&&""!==t.message.trim()&&t.targetCount>0;default:return!1}},getValidConfigCount(){return this.selectedDevices.filter(e=>this.isDeviceConfigValid(e)).length},hasValidConfigs(){return this.getValidConfigCount()>0},async executeFunction(){if(this.canExecute){this.executing=!0;try{console.log("开始执行闲鱼自动化任务");const e={};for(const i of this.selectedDevices){const t=this.$refs[`config_${i}`];if(!t||!t[0])return console.error(`无法获取设备 ${i} 的配置`),void this.$message.error(`无法获取设备 ${i} 的配置`);e[i]=t[0].getConfig()}const t={functionType:this.selectedFunction,deviceConfigs:e,deviceIds:this.selectedDevices};console.log("闲鱼自动化执行参数:",t);const s=await this.$http.post("/api/xianyu/execute",t);s.data.success?(this.$message.success("任务开始执行"),this.selectedDevices.forEach(t=>{this.$store.dispatch("xianyu/taskStarted",{functionType:this.selectedFunction,taskId:s.data.taskId||Date.now().toString(),logId:`${s.data.taskId||Date.now().toString()}_${t}`,config:e[t],deviceId:t})}),this.selectedDevices.forEach(t=>{this.$root.$emit("xianyu-task-started",{functionType:this.selectedFunction,deviceId:t,config:e[t]})})):this.$message.error("执行失败: "+s.data.message)}catch(e){console.error("执行闲鱼自动化任务失败:",e),this.$message.error("执行失败: "+e.message)}finally{this.executing=!1}}},goToLogs(){this.$router.push("/xianyu-logs")},initSocketListeners(){const e=this.$socket;e&&e.connected&&(console.log("闲鱼自动化页面: 初始化Socket事件监听"),e.on("device_status_update",this.handleDeviceStatusUpdate),e.on("device_status_changed",this.handleDeviceStatusChanged),e.on("device_offline",this.handleDeviceOffline),e.on("xianyu_task_started",this.handleTaskStartedFromBackend),e.on("xianyu_status_update",this.handleStatusUpdate),e.on("xianyu_task_update",this.handleStatusUpdate),e.on("xianyu_task_completed",this.handleTaskCompleted),e.on("xianyu_task_stopped",this.handleTaskStopped),e.on("server_shutdown",this.handleServerShutdown),e.on("disconnect",this.handleSocketDisconnect),e.on("xianyu_task_stopped",this.handleTaskStopped),e.on("xianyu_execution_completed",this.handleExecutionCompleted),console.log("📡 [XianyuAutomation] 使用WebSocketManager的全局客户端连接"))},cleanupSocketListeners(){const e=this.$socket;e&&(e.off("device_status_update",this.handleDeviceStatusUpdate),e.off("device_status_changed",this.handleDeviceStatusChanged),e.off("device_offline",this.handleDeviceOffline),e.off("xianyu_task_started",this.handleTaskStartedFromBackend),e.off("xianyu_status_update",this.handleStatusUpdate),e.off("xianyu_task_update",this.handleStatusUpdate),e.off("xianyu_task_completed",this.handleTaskCompleted),e.off("xianyu_task_stopped",this.handleTaskStopped),e.off("xianyu_execution_completed",this.handleExecutionCompleted),e.off("server_shutdown",this.handleServerShutdown),e.off("disconnect",this.handleSocketDisconnect))},handleDeviceStatusUpdate(e){console.log("闲鱼自动化: 收到设备状态更新事件:",e),this.$store.dispatch("device/updateDeviceStatus",{deviceId:e.deviceId,status:e.status,lastSeen:e.lastSeen||new Date}),"online"===e.status&&(console.log(`闲鱼自动化: 设备 ${e.deviceId} 状态变为在线，清理执行状态`),this.clearDeviceExecutionState(e.deviceId),this.$root.$emit("device-status-updated",{deviceId:e.deviceId,status:e.status})),this.loadDevices()},handleDeviceStatusChanged(e){console.log("闲鱼自动化: 收到设备状态变化事件:",e);const{type:t,deviceId:s}=e;"device_disconnected"===t&&this.handleDeviceDisconnected(s)},handleDeviceOffline(e){const{deviceId:t}=e;this.handleDeviceDisconnected(t)},handleDeviceDisconnected(e){console.log(`🔴 [XianyuAutomation] 处理设备断开连接: ${e}`),console.log("🔴 [XianyuAutomation] 当前选中设备列表:",this.selectedDevices),console.log("🔴 [XianyuAutomation] 当前设备配置键:",Object.keys(this.deviceConfigs));let t=e,s=this.selectedDevices.indexOf(e);if(-1===s){console.log("🔴 [XianyuAutomation] 直接匹配失败，尝试其他格式");const i=this.$store.getters["device/devices"]||[];console.log("🔴 [XianyuAutomation] 所有设备:",i.map(e=>({id:e.device_id,name:e.device_name})));const o=i.find(t=>t.device_id===e||t.device_name===e||t.ip_address&&e.includes(t.ip_address.replace(/\./g,"_")));o&&(t=o.device_id,s=this.selectedDevices.indexOf(t),console.log(`🔴 [XianyuAutomation] 通过匹配找到设备: ${t}`))}if(console.log(`🔴 [XianyuAutomation] 最终使用的设备ID: ${t}`),console.log("🔴 [XianyuAutomation] 设备在选中列表中的索引:",s),-1!==s?(this.selectedDevices.splice(s,1),console.log("🔴 [XianyuAutomation] 已从选中列表移除设备，新列表:",this.selectedDevices),this.$message.warning(`设备 ${t} 已断开连接，已从选中列表移除`)):console.log(`🔴 [XianyuAutomation] 设备 ${e} 不在选中列表中`),console.log(`🔴 [XianyuAutomation] 检查设备配置，deviceId: ${t}`),this.deviceConfigs[t]&&(this.$delete(this.deviceConfigs,t),console.log(`🔴 [XianyuAutomation] 已清理设备 ${t} 的配置参数`)),this.activeDeviceTab===t&&(this.selectedDevices.length>0?(this.activeDeviceTab=this.selectedDevices[0],console.log(`🔴 [XianyuAutomation] 活跃标签页已切换到: ${this.activeDeviceTab}`)):(this.activeDeviceTab="",console.log("🔴 [XianyuAutomation] 已清空活跃标签页"))),this.clearDeviceExecutionState(t),this.executing){const e=this.selectedDevices.length>0;e||(console.log("🔴 [XianyuAutomation] 没有其他设备在执行，重置执行状态"),this.executing=!1)}this.$root.$emit("device-offline",{deviceId:t}),this.$root.$emit("device-disconnected",{deviceId:t,timestamp:new Date}),this.savePageState(),this.loadDevices(),console.log("🔴 [XianyuAutomation] 设备断开连接处理完成，当前状态:"),console.log("🔴 [XianyuAutomation] - 选中设备:",this.selectedDevices),console.log("🔴 [XianyuAutomation] - 设备配置:",Object.keys(this.deviceConfigs)),console.log("🔴 [XianyuAutomation] - 活跃标签:",this.activeDeviceTab),console.log("🔴 [XianyuAutomation] - 执行状态:",this.executing)},handleServerShutdown(e){console.log("🔴 [XianyuAutomation] 收到服务器关闭事件:",e),this.selectedDevices=[],this.deviceConfigs={},this.activeDeviceTab="",this.$store.dispatch("xianyu/resetGlobalExecutionState"),this.$root.$emit("server-shutdown",e),this.$message.warning("服务器已关闭，所有设备连接已断开"),console.log("✅ [XianyuAutomation] 服务器关闭处理完成")},handleSocketDisconnect(e){if(console.log("🔌 [XianyuAutomation] Socket连接断开:",e),"io server disconnect"===e||"transport close"===e){console.log("🔌 [XianyuAutomation] 检测到服务器关闭，清理所有设备状态");const e=[...this.selectedDevices];e.forEach(e=>{console.log(`🔌 [XianyuAutomation] 清理设备: ${e}`),this.handleDeviceDisconnected(e)}),this.selectedDevices.length>0&&(console.log("🔌 [XianyuAutomation] 强制清空剩余设备选择"),this.selectedDevices=[],this.deviceConfigs={},this.activeDeviceTab=""),this.$store.dispatch("xianyu/resetGlobalExecutionState"),this.$message.warning("服务器连接已断开，所有设备状态已清理")}},clearDeviceExecutionState(e){console.log(`闲鱼自动化: 清理设备 ${e} 的执行状态`),this.$store.dispatch("xianyu/clearDeviceExecutionState",e),0===this.selectedDevices.length&&(console.log("闲鱼自动化: 没有其他设备，重置执行状态"),this.executing=!1,this.executionProgress=0,this.executionStatus="",this.executionLogs=[],this.executionResult=null)},handleTaskStartedFromBackend(e){console.log("闲鱼自动化: 收到后端任务开始事件",e),this.$root.$emit("xianyu-task-started",{functionType:e.functionType,deviceId:e.deviceId,taskId:e.taskId,config:e.config,message:e.message||"任务开始执行"}),console.log("闲鱼自动化: 已转发任务开始事件给配置组件")},handleStatusUpdate(e){console.log("闲鱼自动化: 收到脚本状态更新",e),console.log("闲鱼自动化: 状态更新详情 - stage:",e.stage,"status:",e.status,"progress:",e.progress),"executing"===e.stage||"running"===e.stage?this.$root.$emit("xianyu-task-started",{functionType:"keywordMessage",deviceId:e.deviceId,taskId:e.taskId,message:e.message||"脚本正在执行"}):"completed"===e.stage?this.$root.$emit("xianyu-task-completed",{functionType:"keywordMessage",deviceId:e.deviceId,taskId:e.taskId,message:e.message||"脚本执行完成"}):"error"===e.stage&&this.$root.$emit("xianyu-task-stopped",{functionType:"keywordMessage",deviceId:e.deviceId,taskId:e.taskId,message:e.message||"脚本执行出错"}),"executing"===e.stage||"running"===e.stage?this.$store.dispatch("xianyu/taskStarted",{functionType:"keywordMessage",taskId:e.taskId,logId:e.logId,deviceId:e.deviceId,config:e.config||{}}):"completed"!==e.stage&&"error"!==e.stage&&"failed"!==e.stage||(console.log("闲鱼自动化: 脚本执行结束，重置状态 - stage:",e.stage),this.$store.dispatch("xianyu/setScriptRunning",{functionType:"keywordMessage",isRunning:!1}),"completed"===e.stage&&this.$store.dispatch("xianyu/setScriptCompleted",{functionType:"keywordMessage",isCompleted:!0}),e.deviceId&&this.$store.dispatch("xianyu/clearDeviceExecutionState",e.deviceId),this.$root.$emit("xianyu-task-completed",{functionType:"keywordMessage",deviceId:e.deviceId,taskId:e.taskId,status:e.stage,message:e.message||"脚本执行结束"})),console.log("闲鱼自动化: 已处理状态更新事件",e.stage)},handleTaskCompleted(e){console.log("闲鱼任务完成:",e),e.deviceId&&(this.$store.dispatch("device/clearDeviceExecutionStatus",e.deviceId),console.log(`已清除设备 ${e.deviceId} 的执行状态`)),console.log("闲鱼自动化: 处理任务完成事件，发送给配置组件"),this.$root.$emit("xianyu-task-completed",e)},handleTaskStopped(e){console.log("闲鱼任务停止:",e),e.deviceId&&(this.$store.dispatch("device/clearDeviceExecutionStatus",e.deviceId),console.log(`已清除设备 ${e.deviceId} 的执行状态`)),e.deviceId&&(console.log("闲鱼自动化: 清理设备执行状态",e.deviceId),this.$store.dispatch("xianyu/clearDeviceExecutionState",e.deviceId),this.$store.dispatch("device/updateDeviceStatus",{deviceId:e.deviceId,status:"online",lastSeen:new Date}),this.loadDevices()),this.$root.$emit("xianyu-task-stopped",e)},handleGlobalTaskStopped(e){if(console.log("闲鱼自动化: 收到全局任务停止事件",e),e.deviceId&&(console.log("闲鱼自动化: 清理设备执行状态",e.deviceId),this.$store.dispatch("xianyu/clearDeviceExecutionState",e.deviceId)),e.deviceId&&this.selectedDevices.includes(e.deviceId)){const t=this.selectedDevices.indexOf(e.deviceId);this.selectedDevices.splice(t,1),console.log("闲鱼自动化: 从选中列表中移除已停止的设备",e.deviceId)}this.loadDevices()},handleGlobalResetStates(){console.log("闲鱼自动化: 收到全局重置状态事件"),this.$store.dispatch("xianyu/resetAllStates"),this.selectedDevices=[],this.activeDeviceTab="",this.executing=!1,this.loadDevices(),this.$root.$emit("xianyu-reset-all-states")},handleExecutionCompleted(e){console.log("闲鱼脚本执行完成:",e),console.log("闲鱼自动化: 处理脚本执行完成事件，重置状态"),e.deviceId&&(this.$store.dispatch("device/clearDeviceExecutionStatus",e.deviceId),console.log(`已清除设备 ${e.deviceId} 的执行状态`)),this.$root.$emit("xianyu-task-completed",{functionType:"keywordMessage",deviceId:e.deviceId,taskId:e.taskId,status:e.status,message:e.message,timestamp:e.timestamp}),e.deviceId&&this.clearDeviceExecutionState(e.deviceId)},savePageState(){const e={selectedFunction:this.selectedFunction,selectedDevices:this.selectedDevices,activeDeviceTab:this.activeDeviceTab,deviceConfigs:this.deviceConfigs};localStorage.setItem("xianyu_automation_state",JSON.stringify(e))},async restorePageState(){try{const e=localStorage.getItem("xianyu_automation_state");if(e){const t=JSON.parse(e);this.selectedFunction=t.selectedFunction||"";const s=t.selectedDevices||[];if(s.length>0){await this.loadDevices();const e=this.devices.filter(e=>"online"===e.status||!("busy"!==e.status||!this.selectedFunction)&&this.isDeviceExecutingCurrentFunction(e.device_id)).map(e=>e.device_id);console.log("闲鱼自动化: 恢复页面状态 - 可用设备:",e),console.log("闲鱼自动化: 恢复页面状态 - 保存的设备:",s),this.selectedDevices=s.filter(t=>e.includes(t));const t=s.length-this.selectedDevices.length;t>0&&s.length>0&&(console.log(`闲鱼自动化: 已过滤 ${t} 个不可用设备`),e.length>0&&t>1&&this.$message.warning(`已过滤 ${t} 个不可用设备`)),console.log("闲鱼自动化: 恢复页面状态 - 最终选中设备:",this.selectedDevices)}else this.selectedDevices=[];this.activeDeviceTab=t.activeDeviceTab||"",this.deviceConfigs=t.deviceConfigs||{},this.activeDeviceTab&&!this.selectedDevices.includes(this.activeDeviceTab)&&(this.activeDeviceTab=""),this.selectedFunction&&0===this.selectedDevices.length&&(console.log("闲鱼自动化: 尝试自动恢复正在执行的设备"),this.autoRestoreExecutingDevices())}}catch(e){console.warn("恢复页面状态失败:",e)}},async restoreExecutionState(){try{console.log("闲鱼自动化: 开始恢复脚本执行状态..."),await this.$store.dispatch("xianyu/restoreExecutionState"),this.$root.$emit("xianyu-restore-state"),console.log("闲鱼自动化: 脚本执行状态恢复完成")}catch(e){console.error("恢复脚本执行状态失败:",e)}},startAutoRefresh(){this.refreshTimer=setInterval(()=>{this.loadDevices()},6e3)},stopAutoRefresh(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)},getDeviceTabLabel(e){if(!this.devices||!Array.isArray(this.devices)||0===this.devices.length)return e;const t=this.devices.find(t=>t.device_id===e);if(!t)return e;const s="online"===t.status?"🟢":"busy"===t.status?"🟡":"🔴";return`${s} ${t.device_name}`},async executeForDevice(e){if(this.isDeviceConfigValid(e)){this.executing=!0;try{const t=this.$refs[`config_${e}`];if(!t||!t[0])return void this.$message.error("无法获取设备配置");const s=t[0].getConfig(),i=`xianyu_${this.selectedFunction}_${Date.now()}_${e}`,o={functionType:this.selectedFunction,deviceConfigs:{[e]:s},deviceIds:[e],taskId:i};console.log("执行单个设备:",e,o);const n=await this.$http.post("/api/xianyu/execute",o);if(n.data.success){this.$message.success(`设备 ${this.getDeviceName(e)} 开始执行`),console.log("单设备执行成功:",n.data),await this.$store.dispatch("device/setDeviceExecutionStatus",{deviceId:e,functionType:this.selectedFunction,module:"xianyu",startTime:new Date});const t=`${i}_${e}`;console.log("=== 立即发送单设备任务开始事件 ==="),this.$root.$emit("xianyu-task-started",{functionType:this.selectedFunction,deviceId:e,taskId:t,config:s,message:`设备 ${this.getDeviceName(e)} 开始执行闲鱼自动化任务`})}else this.$message.error("执行失败: "+n.data.message),console.error("单设备执行失败:",n.data)}catch(t){console.error("执行单设备失败:",t),this.$message.error("执行失败: "+(t.response?.data?.message||t.message))}finally{this.executing=!1}}else this.$message.warning("请先完善设备配置")},async executeAllDevices(){if(this.hasValidConfigs()){this.executing=!0;try{const e={},t=[];for(const n of this.selectedDevices)if(this.isDeviceConfigValid(n)){const s=this.$refs[`config_${n}`];s&&s[0]&&(e[n]=s[0].getConfig(),t.push(n))}const s=`xianyu_${this.selectedFunction}_batch_${Date.now()}`,i={functionType:this.selectedFunction,deviceConfigs:e,deviceIds:t,taskId:s};console.log("批量执行所有设备:",i);const o=await this.$http.post("/api/xianyu/execute",i);o.data.success?(this.$message.success(`已开始批量执行 ${t.length} 个设备`),console.log("批量执行成功:",o.data),console.log("=== 立即发送批量任务开始事件 ==="),t.forEach(t=>{const i=`${s}_${t}`;this.$root.$emit("xianyu-task-started",{functionType:this.selectedFunction,deviceId:t,taskId:i,config:e[t],message:`设备 ${this.getDeviceName(t)} 开始执行闲鱼自动化任务`})})):(this.$message.error("批量执行失败: "+o.data.message),console.error("批量执行失败:",o.data))}catch(e){console.error("批量执行失败:",e),this.$message.error("批量执行失败: "+(e.response?.data?.message||e.message))}finally{this.executing=!1}}else this.$message.warning("请先完善设备配置")},copyConfigToAll(){if(console.log("🔧 开始复制配置到所有设备..."),console.log("当前活动设备:",this.activeDeviceTab),console.log("选中的设备:",this.selectedDevices),!this.activeDeviceTab||this.selectedDevices.length<=1)return void this.$message.warning("请先选择要复制的设备配置");const e=this.$refs[`config_${this.activeDeviceTab}`];if(console.log("源设备配置组件引用:",e),!e||!e[0])return this.$message.error("无法获取源设备配置"),void console.error("❌ 无法获取源设备配置组件");const t=e[0].getConfig();if(console.log("📋 源设备配置:",t),!t||0===Object.keys(t).length)return this.$message.warning("源设备配置为空，无法复制"),void console.warn("⚠️ 源设备配置为空");let s=0,i=[];for(const n of this.selectedDevices)if(n!==this.activeDeviceTab){console.log(`🔧 正在复制配置到设备: ${n}`);const e=this.$refs[`config_${n}`];if(console.log(`设备 ${n} 配置组件引用:`,e),e&&e[0]&&e[0].setConfig)try{e[0].setConfig(t),s++,console.log(`✅ 配置已复制到设备: ${n}`);const i=e[0].getConfig();console.log(`设备 ${n} 复制后的配置:`,i)}catch(o){console.error(`❌ 复制配置到设备 ${n} 失败:`,o),i.push(n)}else console.error(`❌ 设备 ${n} 的配置组件无效`),i.push(n)}if(console.log(`📊 复制结果: 成功 ${s} 个, 失败 ${i.length} 个`),s>0){let e=`已复制配置到 ${s} 个设备`;i.length>0&&(e+=`，${i.length} 个设备复制失败`),this.$message.success(e)}else this.$message.warning("没有可复制的目标设备或复制失败")},resetAllStates(){this.$confirm("确定要重置所有脚本执行状态吗？","确认重置",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{this.$store.dispatch("xianyu/resetAllStates"),this.$root.$emit("xianyu-reset-all-states"),this.$message.success("所有状态已重置")}).catch(()=>{})},cleanupTestData(){this.$confirm("确定要清理所有测试数据吗？这将终止所有任务并清空执行日志！","确认清理",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{const e=await this.$http.post("/api/xianyu/stop-all");console.log("终止所有任务结果:",e.data),await new Promise(e=>setTimeout(e,1e3));const t=await this.$http.delete("/api/xianyu/logs");console.log("清空日志结果:",t.data),this.$store.dispatch("xianyu/resetAllStates"),this.$root.$emit("xianyu-reset-all-states"),this.$message.success("测试数据清理完成！")}catch(e){console.error("清理测试数据失败:",e),this.$message.error("清理失败: "+(e.response?.data?.message||e.message))}}).catch(()=>{})}}},v=p,y=(0,d.A)(v,i,o,!1,null,"70c50462",null),f=y.exports}}]);