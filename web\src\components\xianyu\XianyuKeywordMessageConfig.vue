<template>
  <div class="xianyu-keyword-message-config">
    <el-form :model="config" label-width="120px">
      <!-- 闲鱼应用选择 -->
      <el-form-item label="闲鱼应用">
        <el-select
          v-model="config.selectedApp"
          placeholder="请选择要使用的闲鱼应用"
          @change="onAppSelectionChange"
          :loading="loadingApps"
          style="width: 100%"
        >
          <el-option
            v-for="app in xianyuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          选择设备上要使用的闲鱼应用版本
        </div>
      </el-form-item>

      <el-form-item label="搜索关键词">
        <el-input
          v-model="config.keyword"
          placeholder="请输入搜索关键词"
          maxlength="50"
          show-word-limit
          @input="onInputChange"
          @blur="onInputChange"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          输入要搜索的商品关键词，如：手机、电脑、相机等
        </div>
      </el-form-item>

      <el-form-item label="私信内容">
        <el-input
          v-model="config.message"
          type="textarea"
          :rows="3"
          placeholder="请输入私信内容"
          maxlength="200"
          show-word-limit
          @input="onInputChange"
          @blur="onInputChange"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          发送给卖家的私信内容，建议简洁明了
        </div>
      </el-form-item>

      <el-form-item label="目标私信数量">
        <el-input-number
          v-model="config.targetCount"
          :min="1"
          :max="100"
          placeholder="数量"
          @change="onInputChange"
        />
        <span style="margin-left: 10px; color: #909399;">本次执行要私信的卖家数量</span>
      </el-form-item>

      <el-form-item label="调试模式">
        <el-switch
          v-model="config.debugMode"
          active-text="开启"
          inactive-text="关闭"
          @change="onInputChange"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          开启调试模式时不会真正发送消息，仅模拟执行过程
        </div>
      </el-form-item>

      <el-form-item label="操作间隔">
        <el-input-number
          v-model="config.operationDelay"
          :min="1"
          :max="10"
          placeholder="秒"
          @change="onInputChange"
        />
        <span style="margin-left: 10px; color: #909399;">每个操作步骤间隔时间（秒）</span>
      </el-form-item>

      <el-form-item label="安全设置">
        <el-checkbox-group v-model="config.safetyOptions">
          <el-checkbox label="skipChatted">跳过已私信的帖子</el-checkbox>
          <el-checkbox label="saveProgress">保存执行进度</el-checkbox>
          <el-checkbox label="autoStop">异常时自动停止</el-checkbox>
        </el-checkbox-group>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          • 跳过已私信的帖子：避免重复私信同一个卖家<br>
          • 保存执行进度：记录已私信的帖子，下次执行时跳过<br>
          • 异常时自动停止：遇到错误时自动停止执行
        </div>
      </el-form-item>

      <el-alert
        title="使用提醒"
        type="info"
        :closable="false"
        show-icon
      >
        <div>
          • 请先选择对应的闲鱼应用版本，确保应用已安装并可正常使用<br>
          • 私信内容请遵守平台规则，避免发送广告或违规内容<br>
          • 建议设置合理的私信数量，避免频繁操作被限制<br>
          • 执行过程中请勿手动操作手机<br>
          • 如果应用启动失败，请检查应用版本选择是否正确
        </div>
      </el-alert>

      <!-- 脚本控制按钮 -->
      <el-form-item label="脚本控制">
        <el-button
          type="danger"
          size="small"
          @click="stopScript"
          :disabled="!isScriptRunning"
        >
          停止脚本
        </el-button>
        <el-button
          type="primary"
          size="small"
          icon="el-icon-refresh-right"
          @click="refreshScriptState"
          style="margin-left: 10px;"
          title="检查并同步脚本执行状态"
        >
          刷新状态
        </el-button>
        <el-button
          type="warning"
          size="small"
          icon="el-icon-refresh"
          @click="resetScriptStateManually"
          v-if="isScriptRunning"
          style="margin-left: 10px;"
          title="如果脚本状态显示异常，点击此按钮重置状态"
        >
          重置状态
        </el-button>

        <el-button
          type="info"
          size="small"
          icon="el-icon-chat-dot-round"
          @click="openChatRecordsManager"
          style="margin-left: 10px;"
        >
          管理私聊记录
        </el-button>
        <span v-if="isScriptRunning" style="margin-left: 10px; color: #67C23A; font-size: 12px;">
          脚本正在执行中...
        </span>
        <span v-else-if="isScriptCompleted" style="margin-left: 10px; color: #409EFF; font-size: 12px;">
          脚本执行完成，1分钟后可重新执行
        </span>
        <span v-else style="margin-left: 10px; color: #909399; font-size: 12px;">
          脚本未在运行
        </span>
      </el-form-item>
    </el-form>

    <!-- 实时状态显示 -->
    <el-card class="status-card" v-if="isScriptRunning || isScriptCompleted || currentTaskId">
      <div slot="header" class="clearfix">
        <span>实时执行状态</span>
        <el-tag
          :type="isScriptRunning ? 'warning' : (isScriptCompleted ? 'success' : 'info')"
          size="small"
          style="float: right;"
        >
          {{ isScriptRunning ? '执行中' : (isScriptCompleted ? '已完成' : '等待开始') }}
        </el-tag>
      </div>

      <div class="status-content">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="status-item">
              <div class="status-label">已私信数量</div>
              <div class="status-value">{{ successCount }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="status-item">
              <div class="status-label">失败数量</div>
              <div class="status-value">{{ failedCount }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="status-item">
              <div class="status-label">已处理步骤</div>
              <div class="status-value">{{ processedStepCount }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="status-item">
              <div class="status-label">搜索尝试次数</div>
              <div class="status-value">{{ searchAttemptCount }}</div>
            </div>
          </el-col>
        </el-row>

        <div class="current-status">
          <div class="status-label">当前状态</div>
          <div class="status-text">{{ currentStatus }}</div>
        </div>

        <div class="progress-bar" v-if="isScriptRunning">
          <el-progress
            :percentage="getProgressPercentage()"
            :status="isScriptCompleted ? 'success' : 'active'"
            :stroke-width="8"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import xianyuAppSelector from '@/mixins/xianyuAppSelector'

export default {
  name: 'XianyuKeywordMessageConfig',
  mixins: [xianyuAppSelector],
  props: {
    deviceId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      config: {
        keyword: '',
        message: '',
        targetCount: 10,
        debugMode: true,
        operationDelay: 2,
        safetyOptions: ['skipChatted', 'saveProgress', 'autoStop'],
        selectedApp: '' // 选择的闲鱼应用，从数据库加载后自动选择第一个
      },
      isScriptRunning: false,
      isScriptCompleted: false,
      completionTimer: null,
      // 实时状态数据
      successCount: 0,
      failedCount: 0,
      processedStepCount: 0,
      searchAttemptCount: 0,
      currentStatus: '等待开始',
      // WebSocket相关
      socket: null,
      currentTaskId: null,
      currentLogId: null
    }
  },
  computed: {
    isConfigValid() {
      return this.config.keyword.trim() !== '' &&
             this.config.message.trim() !== '' &&
             this.config.targetCount > 0 &&
             this.config.selectedApp.trim() !== ''
    },
    // 计算执行进度百分比
    getProgressPercentage() {
      return () => {
        if (!this.isScriptRunning && !this.isScriptCompleted) return 0
        if (this.isScriptCompleted) return 100

        // 根据已私信数量和目标数量计算进度
        if (this.config.targetCount > 0) {
          const progress = Math.min((this.successCount / this.config.targetCount) * 100, 95)
          return Math.round(progress)
        }

        // 如果没有目标数量，根据处理步骤数估算进度
        return Math.min(this.processedStepCount * 5, 90)
      }
    }
  },
  watch: {
    // 监听设备ID变化，重新加载状态
    deviceId: {
      handler(newDeviceId, oldDeviceId) {
        if (newDeviceId !== oldDeviceId) {
          console.log('设备ID变化，重新加载配置组件状态:', newDeviceId)
          this.restoreComponentState()
        }
      },
      immediate: false
    },

    // 监听Vuex中的脚本运行状态变化
    '$store.state.xianyu.functionStates.keywordMessage.isScriptRunning': {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          console.log('Vuex脚本运行状态变化:', newValue)
          this.isScriptRunning = newValue
        }
      },
      immediate: false
    },

    // 监听Vuex中的脚本完成状态变化
    '$store.state.xianyu.functionStates.keywordMessage.isScriptCompleted': {
      handler(newValue, oldValue) {
        if (newValue !== oldValue) {
          console.log('Vuex脚本完成状态变化:', newValue)
          this.isScriptCompleted = newValue
        }
      },
      immediate: false
    }
  },
  mounted() {
    // 记录组件挂载时间，用于避免路由切换时立即重置状态
    this._mountTime = Date.now()

    console.log('[XianyuKeywordMessageConfig] 组件已挂载')
    console.log('[XianyuKeywordMessageConfig] 挂载时的初始状态:', {
      currentTaskId: this.currentTaskId,
      currentLogId: this.currentLogId,
      deviceId: this.deviceId
    })

    this.initEventListeners()
    this.loadSavedConfig()

    // 恢复组件状态（异步）
    console.log('[XianyuKeywordMessageConfig] 开始调用状态恢复方法...')
    try {
      this.restoreComponentState().then(() => {
        console.log('[XianyuKeywordMessageConfig] 状态恢复完成，开始验证状态一致性')
        console.log('[XianyuKeywordMessageConfig] 恢复后的状态:', {
          currentTaskId: this.currentTaskId,
          currentLogId: this.currentLogId,
          deviceId: this.deviceId,
          isScriptRunning: this.isScriptRunning
        })

        // 验证状态一致性（延迟执行，避免路由切换时立即重置状态）
        setTimeout(() => {
          // 再次检查组件是否还存在且挂载时间足够长
          if (this._mountTime && (Date.now() - this._mountTime) > 8000) {
            this.validateStateConsistency()
          } else {
            console.log('[XianyuKeywordMessageConfig] 组件挂载时间不足8秒，跳过状态验证')
          }
        }, 5000) // 延迟5秒，给组件足够时间完成初始化

        // 初始化Socket连接
        this.initializeSocket()
      }).catch(error => {
        console.error('[XianyuKeywordMessageConfig] 状态恢复失败:', error)
        console.error('[XianyuKeywordMessageConfig] 错误堆栈:', error.stack)
        // 即使恢复失败也要初始化Socket连接
        this.initializeSocket()
      })
    } catch (syncError) {
      console.error('[XianyuKeywordMessageConfig] 调用状态恢复方法时发生同步错误:', syncError)
      console.error('[XianyuKeywordMessageConfig] 同步错误堆栈:', syncError.stack)
      // 发生同步错误时也要初始化Socket连接
      this.initializeSocket()
    }

    // 确保初始配置被发送到父组件
    this.$nextTick(() => {
      this.onInputChange()
    })
  },
  beforeDestroy() {
    console.log('🔧 [XianyuKeywordMessageConfig] 组件即将销毁，执行清理操作')

    // 保存组件状态
    this.saveComponentState()

    // 不再断开socket连接，让WebSocketManager统一管理
    console.log('🔧 [XianyuKeywordMessageConfig] 保持WebSocket连接，仅清理事件监听')

    this.cleanupEventListeners()
    if (this.completionTimer) {
      clearTimeout(this.completionTimer)
    }

    console.log('🔧 [XianyuKeywordMessageConfig] 组件清理完成')
  },
  methods: {
    initEventListeners() {
      // 监听脚本开始事件
      this.$root.$on('xianyu-task-started', this.handleTaskStarted)
      // 监听脚本停止事件
      this.$root.$on('xianyu-task-stopped', this.handleTaskStopped)
      // 监听脚本完成事件
      this.$root.$on('xianyu-task-completed', this.handleTaskCompleted)
      // 监听设备离线事件
      this.$root.$on('device-offline', this.handleDeviceOffline)
      // 监听设备状态更新事件
      this.$root.$on('device-status-updated', this.handleDeviceStatusUpdated)
      // 监听状态恢复事件
      this.$root.$on('xianyu-restore-state', this.handleStateRestore)
      // 监听全局重置状态事件
      this.$root.$on('xianyu-reset-all-states', this.handleGlobalResetStates)
    },

    cleanupEventListeners() {
      this.$root.$off('xianyu-task-started', this.handleTaskStarted)
      this.$root.$off('xianyu-task-stopped', this.handleTaskStopped)
      this.$root.$off('xianyu-task-completed', this.handleTaskCompleted)
      this.$root.$off('device-offline', this.handleDeviceOffline)
      this.$root.$off('device-status-updated', this.handleDeviceStatusUpdated)
      this.$root.$off('xianyu-restore-state', this.handleStateRestore)
      this.$root.$off('xianyu-reset-all-states', this.handleGlobalResetStates)
    },

    handleTaskStarted(data) {
      if (data.functionType === 'keywordMessage' && data.deviceId === this.deviceId) {
        this.isScriptRunning = true
        this.isScriptCompleted = false
        this.currentTaskId = data.taskId || Date.now().toString() // 设置当前任务ID
        console.log('闲鱼关键词私信脚本开始执行:', data)
        console.log('设置currentTaskId:', this.currentTaskId)

        // 立即更新Vuex store中的任务状态
        this.$store.dispatch('xianyu/taskStarted', {
          functionType: 'keywordMessage',
          taskId: data.taskId || Date.now().toString(),
          logId: data.logId || Date.now().toString(),
          deviceId: this.deviceId,
          config: data.config || this.config
        })

        // 保存状态到Vuex
        this.saveComponentState()
      }
    },

    handleTaskStopped(data) {
      console.log('[XianyuKeywordMessageConfig] 收到任务停止事件:', data)
      console.log('[XianyuKeywordMessageConfig] 当前设备ID:', this.deviceId)
      console.log('[XianyuKeywordMessageConfig] 当前脚本运行状态:', this.isScriptRunning)

      // 更宽松的匹配条件：只要设备ID匹配就处理
      if (data.deviceId === this.deviceId) {
        console.log('[XianyuKeywordMessageConfig] 设备ID匹配，处理任务停止事件')

        // 检查functionType是否匹配（如果有的话）
        const functionTypeMatches = !data.functionType ||
                                   data.functionType === 'keywordMessage' ||
                                   data.functionType === 'keyword_message' ||
                                   data.functionType.includes('keyword') ||
                                   data.functionType.includes('Message')

        console.log('[XianyuKeywordMessageConfig] 功能类型匹配检查:', {
          dataFunctionType: data.functionType,
          matches: functionTypeMatches
        })

        if (functionTypeMatches) {
          console.log('[XianyuKeywordMessageConfig] 功能类型匹配，重置脚本状态')

          this.isScriptRunning = false
          this.isScriptCompleted = false
          this.currentTaskId = null // 清除当前任务ID
          this.currentLogId = null // 清除当前日志ID

          // 重置实时状态数据
          this.successCount = 0
          this.failedCount = 0
          this.processedStepCount = 0
          this.searchAttemptCount = 0
          this.currentStatus = '等待开始'

          console.log('闲鱼关键词私信脚本已停止:', data)

          // 清理Vuex store中的任务状态
          this.$store.dispatch('xianyu/clearDeviceExecutionState', this.deviceId)

          // 保存状态到Vuex
          this.saveComponentState()

          console.log('[XianyuKeywordMessageConfig] 脚本状态重置完成')
        } else {
          console.log('[XianyuKeywordMessageConfig] 功能类型不匹配，忽略停止事件')
        }
      } else {
        console.log('[XianyuKeywordMessageConfig] 设备ID不匹配，忽略停止事件')
      }
    },

    handleTaskCompleted(data) {
      console.log('[XianyuKeywordMessageConfig] 收到任务完成事件:', data)
      console.log('[XianyuKeywordMessageConfig] 当前设备ID:', this.deviceId)

      // 更宽松的匹配条件：只要设备ID匹配就处理
      if (data.deviceId === this.deviceId) {
        console.log('[XianyuKeywordMessageConfig] 设备ID匹配，处理任务完成事件')

        // 检查functionType是否匹配（如果有的话）
        const functionTypeMatches = !data.functionType ||
                                   data.functionType === 'keywordMessage' ||
                                   data.functionType === 'keyword_message' ||
                                   data.functionType.includes('keyword') ||
                                   data.functionType.includes('Message')

        console.log('[XianyuKeywordMessageConfig] 功能类型匹配检查:', {
          dataFunctionType: data.functionType,
          matches: functionTypeMatches
        })

        if (functionTypeMatches) {
          console.log('[XianyuKeywordMessageConfig] 功能类型匹配，设置脚本完成状态')

          this.isScriptRunning = false
          this.isScriptCompleted = true
          this.currentTaskId = null // 清除当前任务ID
          this.currentLogId = null // 清除当前日志ID

          console.log('闲鱼关键词私信脚本执行完成:', data)

          // 清理Vuex store中的任务状态
          this.$store.dispatch('xianyu/clearDeviceExecutionState', this.deviceId)

          // 保存状态到Vuex
          this.saveComponentState()

          // 1分钟后只重置完成状态，保留配置参数
          this.completionTimer = setTimeout(() => {
            this.isScriptCompleted = false
            // 保存状态到Vuex
            this.saveComponentState()
            console.log('[XianyuKeywordMessageConfig] 完成状态已重置')
            // 不重置配置参数，让用户可以继续使用相同配置
          }, 60000)

          console.log('[XianyuKeywordMessageConfig] 脚本完成状态设置完成')
        } else {
          console.log('[XianyuKeywordMessageConfig] 功能类型不匹配，忽略完成事件')
        }
      } else {
        console.log('[XianyuKeywordMessageConfig] 设备ID不匹配，忽略完成事件')
      }
    },

    handleDeviceOffline(data) {
      if (data.deviceId === this.deviceId) {
        console.log('设备离线，重置闲鱼配置组件状态:', data)

        // 重置脚本执行状态
        this.isScriptRunning = false
        this.isScriptCompleted = false

        // 清除定时器
        if (this.completionTimer) {
          clearTimeout(this.completionTimer)
          this.completionTimer = null
        }

        // 清理Vuex store中的任务状态
        this.$store.dispatch('xianyu/clearDeviceExecutionState', this.deviceId)

        // 保存状态到Vuex
        this.saveComponentState()
      }
    },

    handleDeviceStatusUpdated(data) {
      if (data.deviceId === this.deviceId && data.status === 'online') {
        console.log('设备状态更新为在线，检查是否需要重置脚本状态:', data)

        // 如果设备变为在线且当前有脚本在运行，可能是停止操作导致的
        if (this.isScriptRunning) {
          console.log('设备变为在线，重置脚本执行状态')
          this.isScriptRunning = false
          this.isScriptCompleted = false
          this.currentTaskId = null

          // 清理Vuex store中的任务状态
          this.$store.dispatch('xianyu/clearDeviceExecutionState', this.deviceId)

          // 保存状态到Vuex
          this.saveComponentState()
        }
      }
    },

    handleStateRestore() {
      console.log('闲鱼配置组件: 收到状态恢复事件')

      // 从Vuex恢复状态
      this.restoreComponentState()
    },

    handleGlobalResetStates() {
      console.log('闲鱼配置组件: 收到全局重置状态事件')

      // 重置脚本执行状态
      this.isScriptRunning = false
      this.isScriptCompleted = false

      // 清除定时器
      if (this.completionTimer) {
        clearTimeout(this.completionTimer)
        this.completionTimer = null
      }

      // 重置配置参数到默认值
      this.resetConfigToDefault()

      console.log('闲鱼配置组件状态已重置')
    },

    onInputChange() {
      this.$emit('config-change', {
        deviceId: this.deviceId,
        config: { ...this.config },
        isValid: this.isConfigValid
      })
      this.saveConfig()
    },

    // 应用选择变化处理
    onAppSelectionChange() {
      console.log('闲鱼应用选择变化:', this.config.selectedApp)

      // 验证应用选择
      const validation = this.validateAppSelection(this.config.selectedApp)
      if (!validation.valid) {
        this.$message.warning(validation.message)
        return
      }

      console.log('选择的闲鱼应用信息:', validation.app)
      this.onInputChange()
    },

    stopScript() {
      if (this.isScriptRunning) {
        this.$emit('stop-script', {
          deviceId: this.deviceId,
          functionType: 'keywordMessage'
        })

        // 立即更新本地状态
        this.isScriptRunning = false
        this.isScriptCompleted = false

        // 清理Vuex store中的任务状态
        this.$store.dispatch('xianyu/clearDeviceExecutionState', this.deviceId)

        // 保存状态到Vuex
        this.saveComponentState()
      }
    },

    // 刷新脚本状态
    async refreshScriptState() {
      console.log('[XianyuKeywordMessageConfig] 用户手动刷新脚本状态')

      if (!this.deviceId) {
        this.$message.warning('请先选择设备')
        return
      }

      try {
        this.$message.info('正在检查脚本执行状态...')

        // 使用备选验证方案（避免API问题）
        await this.fallbackStateValidation()

        this.$message.success('脚本状态已刷新')

      } catch (error) {
        console.error('[XianyuKeywordMessageConfig] 刷新脚本状态失败:', error)
        this.$message.error('刷新状态失败: ' + (error.message || '未知错误'))
      }
    },

    // 手动重置脚本状态
    async resetScriptStateManually() {
      try {
        const result = await this.$confirm(
          '确定要重置脚本状态吗？这将清除当前的执行状态信息。',
          '重置脚本状态',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        if (result) {
          console.log('[XianyuKeywordMessageConfig] 用户手动重置脚本状态')
          this.resetScriptState('用户手动重置')
          this.$message.success('脚本状态已重置')
        }
      } catch (error) {
        // 用户取消操作
        console.log('[XianyuKeywordMessageConfig] 用户取消重置操作')
      }
    },



    openChatRecordsManager() {
      console.log('打开私聊记录管理页面，设备ID:', this.deviceId)

      // 通过路由跳转到私聊记录管理页面
      this.$router.push({
        name: 'XianyuChatRecords',
        params: {
          deviceId: this.deviceId
        },
        query: {
          deviceName: `设备_${this.deviceId}`,
          from: 'xianyu-automation'
        }
      })
    },

    saveConfig() {
      try {
        const key = `xianyu_keyword_message_config_${this.deviceId}`
        localStorage.setItem(key, JSON.stringify(this.config))
      } catch (e) {
        console.warn('保存配置失败:', e)
      }
    },

    loadSavedConfig() {
      try {
        const key = `xianyu_keyword_message_config_${this.deviceId}`
        const saved = localStorage.getItem(key)
        if (saved) {
          const savedConfig = JSON.parse(saved)
          this.config = { ...this.config, ...savedConfig }
          this.onInputChange()
        }
      } catch (e) {
        console.warn('加载配置失败:', e)
      }
    },

    getConfig() {
      return { ...this.config }
    },

    setConfig(config) {
      if (config) {
        // 使用Vue的响应式更新方式，确保界面能正确更新
        this.config = {
          keyword: config.keyword || '',
          message: config.message || '',
          targetCount: config.targetCount || 10,
          debugMode: config.debugMode !== false,
          operationDelay: config.operationDelay || 2,
          safetyOptions: config.safetyOptions || ['skipChatted', 'saveProgress', 'autoStop']
        }

        // 强制触发Vue的响应式更新
        this.$forceUpdate()

        // 触发配置变化事件
        this.$nextTick(() => {
          this.onInputChange()
        })

        console.log('设置闲鱼配置参数:', config)
        console.log('当前配置状态:', this.config)
      }
    },

    resetConfig() {
      this.config = {
        keyword: '',
        message: '',
        targetCount: 10,
        debugMode: true,
        operationDelay: 2,
        safetyOptions: ['skipChatted', 'saveProgress', 'autoStop']
      }
      this.onInputChange()
    },

    // 重置配置参数到默认值
    resetConfigToDefault() {
      console.log('重置闲鱼配置参数到默认值')

      // 重置所有配置参数
      this.config.keyword = ''
      this.config.message = ''
      this.config.targetCount = 10
      this.config.debugMode = true
      this.config.operationDelay = 2
      this.config.safetyOptions = ['skipChatted', 'saveProgress', 'autoStop']

      // 清除保存的配置
      const storageKey = `xianyu_keyword_message_config_${this.deviceId}`
      localStorage.removeItem(storageKey)

      // 触发配置变化事件
      this.onInputChange()

      console.log('闲鱼配置参数已重置完成')
    },

    // 保存组件状态到Vuex
    async saveComponentState() {
      try {
        await this.$store.dispatch('xianyu/setFunctionState', {
          functionType: 'keywordMessage',
          stateData: {
            isScriptRunning: this.isScriptRunning,
            isScriptCompleted: this.isScriptCompleted,
            config: this.config
          }
        })
        
        console.log('闲鱼配置组件状态已保存到Vuex')
      } catch (error) {
        console.error('保存闲鱼配置组件状态失败:', error)
      }
    },

    // 初始化WebSocket连接
    async initializeSocket() {
      try {
        // 使用WebSocket管理器
        const { getWebSocketManager } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        console.log('🔧 [XianyuKeywordMessageConfig] 开始初始化WebSocket连接')

        // 确保连接
        await this.wsManager.init()

        // 获取socket实例用于事件监听
        this.socket = this.wsManager.socket

        // 注册Web客户端 - 不需要重复注册，WebSocket管理器已经处理了用户认证
        // this.wsManager.emit('web_client_connect', { userId: 'xianyu_keyword_message_config' })

        console.log('✅ [XianyuKeywordMessageConfig] WebSocket连接成功')

        // 监听所有事件进行调试
        this.socket.onAny((eventName, data) => {
          if (eventName.includes('xianyu') || eventName.includes('test')) {
            console.log(`🔍 [XianyuKeywordMessageConfig] 收到WebSocket事件: ${eventName}`, data)
          }
        })

        // 监听任务开始事件
        this.socket.on('xianyu_task_started', (data) => {
          console.log('[XianyuKeywordMessageConfig] 收到WebSocket任务开始事件:', data)
          this.handleTaskStarted(data)
        })

        // 监听任务停止事件
        this.socket.on('xianyu_task_stopped', (data) => {
          console.log('[XianyuKeywordMessageConfig] 收到WebSocket任务停止事件:', data)
          this.handleTaskStopped(data)
        })

        // 监听任务完成事件
        this.socket.on('xianyu_task_completed', (data) => {
          console.log('[XianyuKeywordMessageConfig] 收到WebSocket任务完成事件:', data)
          this.handleTaskCompleted(data)
        })

        // 监听脚本执行完成事件
        this.socket.on('xianyu_execution_completed', (data) => {
          console.log('[XianyuKeywordMessageConfig] 收到WebSocket脚本执行完成事件:', data)
          if (data.deviceId === this.deviceId || !this.deviceId) {
            console.log('[XianyuKeywordMessageConfig] 脚本执行完成，更新状态')

            this.isScriptRunning = false
            this.isScriptCompleted = data.status === 'success'

            // 如果执行成功，1分钟后重置完成状态
            if (data.status === 'success') {
              setTimeout(() => {
                this.isScriptCompleted = false
              }, 60000)
            }

            this.saveComponentState()
          }
        })

        // 监听实时状态更新
        this.socket.on('xianyu_realtime_status', (data) => {
          console.log('🎯 [XianyuKeywordMessageConfig] 收到WebSocket实时状态事件:', data)
          this.handleRealtimeStatus(data)
        })

        // 监听测试广播事件
        this.socket.on('test_realtime_broadcast', (data) => {
          console.log('🧪 [XianyuKeywordMessageConfig] 收到测试广播:', data)
        })

        console.log('✅ [XianyuKeywordMessageConfig] Socket初始化完成')
      } catch (error) {
        console.error('❌ [XianyuKeywordMessageConfig] Socket初始化失败:', error)
      }
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [XianyuKeywordMessageConfig] 收到实时状态数据:', data)
      console.log('📋 [XianyuKeywordMessageConfig] 当前组件taskId:', this.currentTaskId)
      console.log('📋 [XianyuKeywordMessageConfig] 数据中的taskId:', data.taskId)
      console.log('🔍 [XianyuKeywordMessageConfig] taskId匹配:', this.currentTaskId && data.taskId === this.currentTaskId)

      // 临时解决方案：如果currentTaskId为空，但接收到了taskId，尝试恢复
      if (!this.currentTaskId && data.taskId && data.taskId.includes('xianyu')) {
        console.log('🔄 [XianyuKeywordMessageConfig] 检测到taskId为空，尝试从WebSocket数据恢复taskId:', data.taskId)
        this.currentTaskId = data.taskId
        console.log('✅ [XianyuKeywordMessageConfig] 已从WebSocket数据恢复taskId:', this.currentTaskId)

        // 保存恢复的状态
        this.saveComponentState()
      }

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('✅ [XianyuKeywordMessageConfig] taskId匹配，更新实时状态:', data)

        // 更新统计数据
        if (data.successCount !== undefined) {
          this.successCount = data.successCount
          console.log('📊 [XianyuKeywordMessageConfig] 更新成功私信数:', this.successCount)
        }
        if (data.failedCount !== undefined) {
          this.failedCount = data.failedCount
          console.log('📊 [XianyuKeywordMessageConfig] 更新失败私信数:', this.failedCount)
        }
        if (data.processedStepCount !== undefined) {
          this.processedStepCount = data.processedStepCount
          console.log('📊 [XianyuKeywordMessageConfig] 更新已处理步骤数:', this.processedStepCount)
        }
        if (data.searchAttemptCount !== undefined) {
          this.searchAttemptCount = data.searchAttemptCount
          console.log('📊 [XianyuKeywordMessageConfig] 更新搜索尝试次数:', this.searchAttemptCount)
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
          console.log('📊 [XianyuKeywordMessageConfig] 更新当前状态:', this.currentStatus)
        }

        console.log('✅ [XianyuKeywordMessageConfig] 实时状态已更新:', {
          successCount: this.successCount,
          failedCount: this.failedCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        })

        // 强制更新视图
        this.$forceUpdate()
        console.log('🔄 [XianyuKeywordMessageConfig] 已强制更新视图')
      } else {
        console.log('❌ [XianyuKeywordMessageConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新')
      }
    },

    // 保存组件状态
    async saveComponentState() {
      const { saveComponentState } = await import('@/utils/stateManager')

      const stateData = {
        config: this.config,
        currentLogId: this.currentLogId,
        currentTaskId: this.currentTaskId,
        isScriptRunning: this.isScriptRunning,
        isScriptCompleted: this.isScriptCompleted,
        // 保存实时状态数据
        realtimeData: {
          successCount: this.successCount,
          failedCount: this.failedCount,
          processedStepCount: this.processedStepCount,
          searchAttemptCount: this.searchAttemptCount,
          currentStatus: this.currentStatus
        }
      }

      await saveComponentState(this, 'xianyuKeywordMessage', stateData)
      console.log('[XianyuKeywordMessageConfig] 组件状态已保存，currentTaskId:', this.currentTaskId)
    },

    // 验证状态一致性
    async validateStateConsistency() {
      console.log('🔍 [XianyuKeywordMessageConfig] 开始验证状态一致性')

      if (!this.deviceId) {
        console.log('[XianyuKeywordMessageConfig] 设备ID为空，跳过状态验证')
        return
      }

      // 直接使用备选验证方案，避免API 404错误
      console.log('[XianyuKeywordMessageConfig] 使用备选验证方案（避免API问题）')
      await this.fallbackStateValidation()

      // 注释掉API调用，等API修复后再启用
      /*
      try {
        console.log(`[XianyuKeywordMessageConfig] 验证设备 ${this.deviceId} 的执行状态`)

        // 使用新的设备状态检查API
        const statusResponse = await this.$http.get(`/api/xianyu/device-status/${this.deviceId}`)
        const statusData = statusResponse.data.data

        console.log(`[XianyuKeywordMessageConfig] 设备执行状态检查结果:`, statusData)
        console.log(`[XianyuKeywordMessageConfig] 前端状态: isScriptRunning=${this.isScriptRunning}`)

        // 情况1：前端显示脚本运行中，但实际没有运行任务 - 重置状态
        if (this.isScriptRunning && !statusData.isActuallyRunning) {
          console.log('[XianyuKeywordMessageConfig] 前端显示脚本运行中，但实际没有运行任务，重置状态')
          this.resetScriptState('实际没有运行任务')
          this.$message.warning('检测到脚本状态异常，已自动重置状态')
        }
        // 情况2：前端显示脚本未运行，但实际有运行任务 - 恢复状态
        else if (!this.isScriptRunning && statusData.isActuallyRunning) {
          console.log('[XianyuKeywordMessageConfig] 前端显示脚本未运行，但实际有运行任务，恢复状态')
          await this.restoreFromRunningTask()
        }
        // 情况3：状态一致
        else if (this.isScriptRunning && statusData.isActuallyRunning) {
          console.log('[XianyuKeywordMessageConfig] 状态一致，脚本正在运行')
        } else if (!this.isScriptRunning && !statusData.isActuallyRunning) {
          console.log('[XianyuKeywordMessageConfig] 状态一致，脚本未运行')
        }

      } catch (error) {
        console.error('[XianyuKeywordMessageConfig] 状态验证失败:', error)

        // 如果是404错误（设备不存在），重置状态
        if (error.response && error.response.status === 404) {
          console.log('[XianyuKeywordMessageConfig] 设备不存在，重置脚本状态')
          this.resetScriptState('设备不存在')
          this.$message.warning('设备不存在，已重置脚本状态')
        } else {
          // 其他错误（如网络错误、API不可用等），使用备选方案
          console.log('[XianyuKeywordMessageConfig] API不可用，使用备选验证方案')
          await this.fallbackStateValidation()
        }
      }
      */
    },

    // 备选状态验证方案（当API不可用时）
    async fallbackStateValidation() {
      try {
        console.log('[XianyuKeywordMessageConfig] 使用备选方案验证状态')
        console.log('[XianyuKeywordMessageConfig] 当前脚本状态:', {
          isScriptRunning: this.isScriptRunning,
          currentTaskId: this.currentTaskId,
          deviceId: this.deviceId
        })

        // 检查是否有正在执行的任务
        const logsResponse = await this.$http.get('/api/xianyu/logs', {
          params: {
            page: 1,
            limit: 5,
            deviceId: this.deviceId,
            executionStatus: 'running'
          }
        })

        const runningLogs = logsResponse.data.data.logs || []
        console.log('[XianyuKeywordMessageConfig] 查询到的正在执行任务:', runningLogs.length, '个')
        console.log('[XianyuKeywordMessageConfig] 查询响应详情:', logsResponse.data)
        if (runningLogs.length > 0) {
          console.log('[XianyuKeywordMessageConfig] 正在执行的任务详情:', runningLogs)
        }

        // 情况1：前端显示脚本运行中，但实际没有运行任务 - 谨慎重置状态
        if (this.isScriptRunning && runningLogs.length === 0) {
          console.log('[XianyuKeywordMessageConfig] 前端显示脚本运行中，但实际没有运行任务')

          // 检查是否是组件刚挂载（避免路由切换时立即重置）
          const mountTime = Date.now() - (this._mountTime || Date.now())
          if (mountTime < 10000) { // 组件挂载10秒内不自动重置
            console.log('[XianyuKeywordMessageConfig] 组件挂载时间不足10秒，跳过自动重置（避免路由切换误重置）')
            console.log('[XianyuKeywordMessageConfig] 挂载时间:', mountTime + 'ms')
            return
          }

          // 最后一次确认：检查是否有设备正在执行任务
          try {
            const deviceResponse = await this.$http.get(`/api/device/list`)
            const devices = deviceResponse.data.data || []
            const currentDevice = devices.find(d => d.device_id === this.deviceId)

            if (currentDevice && currentDevice.status === 'busy') {
              console.log('[XianyuKeywordMessageConfig] 设备状态为busy，可能有任务在执行，跳过重置')
              return
            }
          } catch (e) {
            console.log('[XianyuKeywordMessageConfig] 检查设备状态失败，跳过重置:', e.message)
            return
          }

          console.log('[XianyuKeywordMessageConfig] 确认需要重置状态')
          console.log('[XianyuKeywordMessageConfig] 清理localStorage中的错误状态')
          // 清理localStorage中的错误状态
          const backupKey = `xianyuKeywordMessage_backup_${this.deviceId}`
          localStorage.removeItem(backupKey)
          this.resetScriptState('实际没有运行任务')
          this.$message.warning('检测到脚本状态异常，已自动重置状态')
        }
        // 情况2：前端显示脚本未运行，但实际有运行任务 - 恢复状态
        else if (!this.isScriptRunning && runningLogs.length > 0) {
          console.log('[XianyuKeywordMessageConfig] 前端显示脚本未运行，但实际有运行任务，恢复状态')
          console.log('[XianyuKeywordMessageConfig] 即将调用restoreFromRunningTask方法')
          console.log('[XianyuKeywordMessageConfig] 正在执行的任务详情:', runningLogs)
          await this.restoreFromRunningTask()
        }
        // 情况3：状态一致
        else if (this.isScriptRunning && runningLogs.length > 0) {
          console.log('[XianyuKeywordMessageConfig] 状态一致，脚本正在运行')
        } else if (!this.isScriptRunning && runningLogs.length === 0) {
          console.log('[XianyuKeywordMessageConfig] 状态一致，脚本未运行')
        }

      } catch (error) {
        console.error('[XianyuKeywordMessageConfig] 备选状态验证失败:', error)
        // 如果查询失败，但前端显示脚本运行中，提供手动重置选项
        if (this.isScriptRunning) {
          console.log('[XianyuKeywordMessageConfig] 查询失败但前端显示运行中，建议手动重置')
          this.$message.warning('无法验证脚本状态，如果脚本未在运行，请点击"重置状态"按钮')
        }
      }
    },

    // 从正在执行的任务中恢复状态
    async restoreFromRunningTask() {
      try {
        // 从执行日志中获取任务信息
        const logsResponse = await this.$http.get('/api/xianyu/logs', {
          params: {
            page: 1,
            limit: 1,
            deviceId: this.deviceId,
            executionStatus: 'running'
          }
        })

        const runningLogs = logsResponse.data.data.logs || []
        console.log('[XianyuKeywordMessageConfig] restoreFromRunningTask查询响应详情:', logsResponse.data)
        console.log('[XianyuKeywordMessageConfig] restoreFromRunningTask查询到的任务数量:', runningLogs.length)
        if (runningLogs.length > 0) {
          console.log('[XianyuKeywordMessageConfig] restoreFromRunningTask正在执行的任务详情:', runningLogs)
        }

        if (runningLogs.length > 0) {
          const latestTask = runningLogs[0]
          console.log('[XianyuKeywordMessageConfig] 恢复脚本执行状态，任务信息:', latestTask)

          // 恢复脚本状态
          this.isScriptRunning = true
          this.isScriptCompleted = false
          this.currentTaskId = latestTask.id
          this.currentLogId = latestTask.id

          // 恢复配置参数
          if (latestTask.configParams) {
            try {
              const configParams = typeof latestTask.configParams === 'string'
                ? JSON.parse(latestTask.configParams)
                : latestTask.configParams
              this.config = { ...this.config, ...configParams }
            } catch (e) {
              console.warn('[XianyuKeywordMessageConfig] 解析配置参数失败:', e)
            }
          }

          // 更新Vuex状态
          this.$store.dispatch('xianyu/setFunctionState', {
            functionType: 'keywordMessage',
            stateData: {
              isScriptRunning: true,
              isScriptCompleted: false,
              currentTaskId: latestTask.id,
              currentLogId: latestTask.id,
              config: this.config
            }
          })

          // 保存状态
          this.saveComponentState()

          this.$message.success('检测到正在执行的任务，已自动恢复脚本状态')
        }
      } catch (error) {
        console.error('[XianyuKeywordMessageConfig] 从正在执行的任务中恢复状态失败:', error)
      }
    },

    // 重置脚本状态
    resetScriptState(reason) {
      console.log(`[XianyuKeywordMessageConfig] 重置脚本状态，原因: ${reason}`)

      this.isScriptRunning = false
      this.isScriptCompleted = false
      this.currentTaskId = null
      this.currentLogId = null

      // 重置实时状态
      this.successCount = 0
      this.failedCount = 0
      this.processedStepCount = 0
      this.searchAttemptCount = 0
      this.currentStatus = '等待开始'

      // 清理Vuex状态
      this.$store.dispatch('xianyu/clearDeviceExecutionState', this.deviceId)

      // 保存状态
      this.saveComponentState()

      console.log('[XianyuKeywordMessageConfig] 脚本状态已重置')
    },

    // 从Vuex恢复组件状态
    async restoreComponentState() {
      console.log('🚀 [XianyuKeywordMessageConfig] restoreComponentState方法被调用')
      try {
        console.log('[XianyuKeywordMessageConfig] 开始恢复组件状态...')
        console.log('[XianyuKeywordMessageConfig] 当前deviceId:', this.deviceId)

        // 先检查Vuex中的状态
        const vuexState = this.$store.getters['xianyu/getFunctionState']('keywordMessage')
        console.log('[XianyuKeywordMessageConfig] Vuex中的状态:', vuexState)

        // 检查localStorage备份
        const backupKey = `xianyuKeywordMessage_backup_${this.deviceId || 'default'}`
        const backupData = localStorage.getItem(backupKey)
        console.log('[XianyuKeywordMessageConfig] localStorage备份键:', backupKey)
        console.log('[XianyuKeywordMessageConfig] localStorage备份数据:', backupData)

        const { restoreComponentState } = await import('@/utils/stateManager')
        const functionState = await restoreComponentState(this, 'xianyuKeywordMessage')

        console.log('[XianyuKeywordMessageConfig] 状态管理工具返回的状态:', functionState)

        if (functionState && Object.keys(functionState).length > 0) {
          console.log('[XianyuKeywordMessageConfig] 恢复组件状态:', functionState)

          // 恢复配置
          if (functionState.config && Object.keys(functionState.config).length > 0) {
            this.config = { ...this.config, ...functionState.config }
          }

          // 恢复任务信息
          this.currentLogId = functionState.currentLogId || null
          this.currentTaskId = functionState.currentTaskId || null
          this.isScriptRunning = functionState.isScriptRunning || false
          this.isScriptCompleted = functionState.isScriptCompleted || false

          // 恢复实时状态数据
          if (functionState.realtimeData) {
            this.successCount = functionState.realtimeData.successCount || 0
            this.failedCount = functionState.realtimeData.failedCount || 0
            this.processedStepCount = functionState.realtimeData.processedStepCount || 0
            this.searchAttemptCount = functionState.realtimeData.searchAttemptCount || 0
            this.currentStatus = functionState.realtimeData.currentStatus || '等待开始'
            console.log('[XianyuKeywordMessageConfig] 实时状态已恢复:', functionState.realtimeData)
          }

          console.log('[XianyuKeywordMessageConfig] 组件状态已恢复，currentTaskId:', this.currentTaskId)

          // 如果恢复的状态显示脚本正在运行，记录日志
          if (this.isScriptRunning) {
            console.log('[XianyuKeywordMessageConfig] 从状态恢复中发现脚本正在运行，将进行状态验证')
          }
        } else {
          console.log('[XianyuKeywordMessageConfig] 没有找到保存的状态，检查是否有正在执行的任务')

          // 如果没有保存的状态，但可能有正在执行的任务，尝试从Vuex恢复
          await this.tryRestoreFromRunningTasks()
        }
      } catch (error) {
        console.error('[XianyuKeywordMessageConfig] 恢复组件状态失败:', error)
      }
    },

    // 尝试从正在执行的任务中恢复状态
    async tryRestoreFromRunningTasks() {
      if (!this.deviceId) {
        console.log('[XianyuKeywordMessageConfig] 设备ID为空，无法从正在执行的任务中恢复')
        return
      }

      try {
        console.log('[XianyuKeywordMessageConfig] 尝试从正在执行的任务中恢复状态')

        // 检查是否有正在执行的任务
        const logsResponse = await this.$http.get('/api/xianyu/logs', {
          params: {
            page: 1,
            limit: 1,
            deviceId: this.deviceId,
            executionStatus: 'running'
          }
        })

        const runningLogs = logsResponse.data.data.logs || []
        console.log(`[XianyuKeywordMessageConfig] 找到 ${runningLogs.length} 个正在执行的任务`)
        console.log('[XianyuKeywordMessageConfig] tryRestoreFromRunningTasks查询响应详情:', logsResponse.data)
        if (runningLogs.length > 0) {
          console.log('[XianyuKeywordMessageConfig] tryRestoreFromRunningTasks正在执行的任务详情:', runningLogs)
        } else {
          console.log('[XianyuKeywordMessageConfig] tryRestoreFromRunningTasks未找到正在执行的任务，清理可能的错误状态')
          // 如果数据库中没有正在执行的任务，但前端可能有错误状态，强制清理
          if (this.isScriptRunning) {
            console.log('[XianyuKeywordMessageConfig] 强制清理错误的脚本运行状态')
            const backupKey = `xianyuKeywordMessage_backup_${this.deviceId}`
            localStorage.removeItem(backupKey)
            this.resetScriptState('数据库中无正在执行的任务')
          }
          return
        }

        if (runningLogs.length > 0) {
          const latestTask = runningLogs[0]
          console.log('[XianyuKeywordMessageConfig] 从正在执行的任务中恢复状态:', latestTask)

          // 恢复脚本状态
          this.isScriptRunning = true
          this.isScriptCompleted = false
          this.currentTaskId = latestTask.id
          this.currentLogId = latestTask.id

          // 恢复配置参数
          if (latestTask.configParams) {
            this.config = { ...this.config, ...latestTask.configParams }
            console.log('[XianyuKeywordMessageConfig] 已恢复配置参数:', latestTask.configParams)
          }

          // 更新Vuex状态
          this.$store.dispatch('xianyu/setFunctionState', {
            functionType: 'keywordMessage',
            stateData: {
              isScriptRunning: true,
              isScriptCompleted: false,
              currentTaskId: latestTask.id,
              currentLogId: latestTask.id,
              config: this.config
            }
          })

          // 保存状态
          this.saveComponentState()

          console.log('[XianyuKeywordMessageConfig] 已从正在执行的任务中恢复脚本状态')
        } else {
          console.log('[XianyuKeywordMessageConfig] 没有找到正在执行的任务')

          // 确保初始化默认值
          this.currentLogId = null
          this.currentTaskId = null
          this.isScriptRunning = false
          this.isScriptCompleted = false
        }

      } catch (error) {
        console.error('[XianyuKeywordMessageConfig] 从正在执行的任务中恢复状态失败:', error)

        // 确保初始化默认值
        this.currentLogId = null
        this.currentTaskId = null
        this.isScriptRunning = false
        this.isScriptCompleted = false
      }
    }
  }
}
</script>

<style scoped>
.xianyu-keyword-message-config {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-form-item {
  margin-bottom: 22px;
}

.el-alert {
  margin-top: 20px;
}

/* 实时状态显示样式 */
.status-card {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
}

.status-content {
  padding: 10px 0;
}

.status-item {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  background: #f8f9fa;
  margin-bottom: 10px;
}

.status-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.status-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.current-status {
  margin-top: 15px;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.current-status .status-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.status-text {
  font-size: 16px;
  color: #303133;
  font-weight: 500;
}

.progress-bar {
  margin-top: 15px;
}
</style>