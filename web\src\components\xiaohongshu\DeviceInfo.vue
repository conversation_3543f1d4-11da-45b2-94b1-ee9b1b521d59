<template>
  <div class="device-info-component">
    <!-- 设备信息卡片 -->
    <el-card class="device-card" style="margin-bottom: 10px;">
      <div slot="header" class="clearfix">
        <span style="font-size: 14px; font-weight: bold; color: #409EFF;">
          <i class="el-icon-mobile-phone"></i> 设备信息
        </span>
        <div style="float: right;">
          <!-- 批量选择按钮组 -->
          <el-button-group v-if="hasDevices && enableBatchSelect" style="margin-right: 8px;">
            <el-button
              size="mini"
              type="primary"
              @click="selectAllDevices"
              :disabled="allDevicesSelected"
            >
              全选
            </el-button>
            <el-button
              size="mini"
              type="default"
              @click="clearAllDevices"
              :disabled="selectedDeviceIds.length === 0"
            >
              清空
            </el-button>
            <el-button
              size="mini"
              type="success"
              @click="showOnlineDevicesDialog"
              :disabled="availableDevices.filter(d => this.canSelectDevice(d)).length === 0"
            >
              批量选择
            </el-button>
          </el-button-group>

          <el-button
            style="padding: 3px 8px; font-size: 12px;"
            type="text"
            @click="refreshDeviceInfo"
            :loading="refreshing"
          >
            刷新
          </el-button>
        </div>
      </div>

      <div class="device-content">
        <div v-if="!hasDevices" class="no-device">
          <i class="el-icon-warning" style="font-size: 24px; color: #E6A23C; margin-bottom: 8px;"></i>
          <p style="color: #909399; margin: 0;">暂无设备连接</p>
          <p style="color: #C0C4CC; font-size: 12px; margin: 4px 0 0 0;">请先连接手机设备</p>
        </div>

        <div v-else class="device-list">
          <!-- 批量选择模式 -->
          <div v-if="enableBatchSelect" class="batch-select-info">
            <el-alert
              :title="`已选择 ${selectedDeviceIds.length} 个设备`"
              type="info"
              :closable="false"
              show-icon
              style="margin-bottom: 10px;"
            >
              <template slot="default">
                <span v-if="selectedDeviceIds.length === 0">请选择要执行脚本的设备</span>
                <span v-else>
                  已选择: {{ getSelectedDeviceNames() }}
                </span>
              </template>
            </el-alert>
          </div>

          <div
            v-for="device in availableDevices"
            :key="device.device_id"
            class="device-item"
            :class="{
              'selected': enableBatchSelect ? selectedDeviceIds.includes(device.device_id) : selectedDeviceId === device.device_id,
              'batch-mode': enableBatchSelect,
              'busy': device.status === 'busy',
              'executing-other': isDeviceExecutingOtherFunction(device)
            }"
            @click="selectDevice(device)"
          >
            <div class="device-header">
              <!-- 批量选择复选框 -->
              <el-checkbox
                v-if="enableBatchSelect"
                :value="selectedDeviceIds.includes(device.device_id)"
                @change="(checked) => toggleDeviceSelection(device, checked)"
                @click.stop
                style="margin-right: 8px;"
              ></el-checkbox>

              <span class="device-name">{{ device.device_name }}</span>
              <el-tag
                :type="getStatusTagType(device.status)"
                size="mini"
              >
                {{ getStatusText(device.status) }}
              </el-tag>
            </div>

            <div class="device-details">
              <div class="device-id">ID: {{ device.device_id }}</div>
              <div v-if="device.device_info" class="device-specs">
                <span v-if="device.device_info.brand && device.device_info.model">
                  {{ device.device_info.brand }} {{ device.device_info.model }}
                </span>
                <span v-if="device.device_info.androidVersion">
                  | Android {{ device.device_info.androidVersion }}
                </span>
                <span v-if="device.device_info.screenWidth && device.device_info.screenHeight">
                  | {{ device.device_info.screenWidth }}×{{ device.device_info.screenHeight }}
                </span>
              </div>
              <div v-if="device.ip_address" class="device-ip">
                IP: {{ device.ip_address }}
              </div>
            </div>

            <div class="device-actions">
              <el-button
                size="mini"
                type="warning"
                @click.stop="disconnectDevice(device)"
                :disabled="device.status !== 'online'"
                style="margin-left: auto;"
              >
                断开
              </el-button>
            </div>
          </div>
        </div>

        <!-- 连接状态指示器 -->
        <div class="connection-status">
          <div class="status-item">
            <span class="status-label">连接状态:</span>
            <span :class="connectionStatusClass">{{ connectionStatusText }}</span>
          </div>
          <div class="status-item">
            <span class="status-label">连接设备:</span>
            <span class="status-value">{{ connectedDevices.length }} / {{ allDevices.length }}</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 批量选择对话框 -->
    <el-dialog
      title="批量选择设备"
      :visible.sync="batchSelectDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="batch-select-dialog">
        <div class="dialog-header">
          <el-alert
            title="请选择要添加到执行列表的设备"
            type="info"
            :closable="false"
            show-icon
            style="margin-bottom: 15px;"
          >
            <template slot="default">
              <span>当前可选设备 {{ availableDevices.filter(d => this.canSelectDevice(d)).length }} 个，已选择 {{ tempSelectedDevices.length }} 个</span>
            </template>
          </el-alert>
        </div>

        <div class="device-selection-list">
          <el-checkbox-group v-model="tempSelectedDevices">
            <div
              v-for="device in availableDevices.filter(d => this.canSelectDevice(d))"
              :key="device.device_id"
              class="batch-device-item"
              :class="{ 
                'already-selected': selectedDeviceIds.includes(device.device_id)
              }"
            >
              <el-checkbox
                :label="device.device_id"
                :disabled="selectedDeviceIds.includes(device.device_id)"
              >
                <div class="device-info-row">
                  <div class="device-main-info">
                    <span class="device-name">{{ device.device_name }}</span>
                    <el-tag type="success" size="mini">在线</el-tag>
                    <el-tag v-if="selectedDeviceIds.includes(device.device_id)" type="warning" size="mini">已选择</el-tag>
                  </div>
                  <div class="device-sub-info">
                    <span class="device-id">ID: {{ device.device_id }}</span>
                    <span v-if="device.ip_address" class="device-ip">IP: {{ device.ip_address }}</span>
                  </div>
                  <div v-if="device.device_info" class="device-specs">
                    <span v-if="device.device_info.brand && device.device_info.model">
                      {{ device.device_info.brand }} {{ device.device_info.model }}
                    </span>
                    <span v-if="device.device_info.androidVersion">
                      | Android {{ device.device_info.androidVersion }}
                    </span>
                  </div>
                </div>
              </el-checkbox>
            </div>
          </el-checkbox-group>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <div class="footer-info">
          <span>将添加 {{ tempSelectedDevices.length }} 个设备到执行列表</span>
        </div>
        <div class="footer-buttons">
          <el-button @click="cancelBatchSelect">取消</el-button>
          <el-button
            type="primary"
            @click="confirmBatchSelect"
            :disabled="tempSelectedDevices.length === 0"
          >
            确认选择 ({{ tempSelectedDevices.length }})
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'DeviceInfo',
  props: {
    // 选中的设备ID（单选模式）
    selectedDeviceId: {
      type: String,
      default: ''
    },
    // 选中的设备ID列表（批量选择模式）
    selectedDeviceIds: {
      type: Array,
      default: () => []
    },
    // 是否启用批量选择模式
    enableBatchSelect: {
      type: Boolean,
      default: false
    },
    // 当前功能类型
    currentFunction: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      allDevices: [],
      refreshing: false,
      wsManager: null,
      connectionType: 'disconnected',
      // 实时刷新相关
      refreshTimer: null,
      refreshInterval: 6000, // 6秒刷新一次
      isAutoRefreshEnabled: true,
      // 批量选择对话框相关
      batchSelectDialogVisible: false,
      tempSelectedDevices: [] // 临时选择的设备ID列表
    }
  },
  computed: {
    hasDevices() {
      return this.availableDevices.length > 0
    },
    // 显示在线和忙碌的设备，不显示离线设备
    availableDevices() {
      return this.allDevices.filter(device =>
        device.status === 'online' || device.status === 'busy'
      )
    },
    // 可选设备（排除忙碌设备）
    selectableDevices() {
      return this.allDevices.filter(device => device.status === 'online')
    },
    // 连接的设备（在线 + 忙碌）
    connectedDevices() {
      return this.allDevices.filter(device =>
        device.status === 'online' || device.status === 'busy'
      )
    },
    onlineDevices() {
      return this.allDevices.filter(device => device.status === 'online')
    },
    connectionStatusText() {
      switch (this.connectionType) {
        case 'websocket':
          return '✅ WebSocket连接'
        case 'long-polling':
          return '📡 长轮询连接'
        default:
          return '❌ 未连接'
      }
    },
    connectionStatusClass() {
      return {
        'status-connected': this.connectionType === 'websocket',
        'status-polling': this.connectionType === 'long-polling',
        'status-disconnected': this.connectionType === 'disconnected'
      }
    },

    // 是否所有可选设备都已选中
    allDevicesSelected() {
      const selectableCount = this.availableDevices.filter(device => this.canSelectDevice(device)).length
      return this.enableBatchSelect &&
             selectableCount > 0 &&
             this.selectedDeviceIds.length === selectableCount
    },

    // 连接设备数量
    connectedDevicesCount() {
      return this.availableDevices.length
    },

    // 在线设备数量
    onlineDevicesCount() {
      return this.availableDevices.filter(device => device.status === 'online').length
    },

    // 忙碌设备数量
    busyDevicesCount() {
      return this.availableDevices.filter(device => device.status === 'busy').length
    }
  },
  async mounted() {
    await this.initWebSocketCommunication()
    await this.loadDevices()
    this.startAutoRefresh()

    // 监听设备选择状态变化事件
    this.$root.$on('device-selection-changed', this.handleDeviceSelectionChanged)
  },
  beforeDestroy() {
    this.stopAutoRefresh()
    this.cleanupEventListeners()

    // 清理根事件监听
    this.$root.$off('device-selection-changed', this.handleDeviceSelectionChanged)
  },
  methods: {
    // 初始化WebSocket通信
    async initWebSocketCommunication() {
      try {
        const { getWebSocketManager, ensureConnection } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        // 确保连接状态
        await ensureConnection()

        // 注册事件监听器
        this.wsManager.on('connection_established', this.handleConnectionEstablished)
        this.wsManager.on('device_status_changed', this.handleDeviceStatusChanged)
        this.wsManager.on('devices_list', this.handleDevicesListUpdate)
        this.wsManager.on('device_status_update', this.handleDeviceStatusUpdate)

        // 监听连接状态变化
        this.wsManager.on('connection_established', () => {
          console.log('🔧 [DeviceInfo] 收到连接建立事件，更新连接状态')
          this.updateConnectionStatus()
        })

        // 不重复初始化连接，使用全局连接

        // 更新连接状态
        this.updateConnectionStatus()

        // 延迟再次更新连接状态，确保状态同步
        setTimeout(() => {
          this.updateConnectionStatus()
        }, 1000)

      } catch (error) {
        console.error('混合通信初始化失败:', error)
        this.$message.error('通信初始化失败: ' + error.message)
      }
    },

    // 处理连接建立
    handleConnectionEstablished(data) {
      // 连接建立日志不需要频繁输出
      if (!this._connectionLogTime || Date.now() - this._connectionLogTime > 60000) { // 1分钟节流
        console.log('设备信息组件: 连接建立', data.type)
        this._connectionLogTime = Date.now()
      }
      this.connectionType = data.type
    },

    // 处理设备状态变化
    handleDeviceStatusChanged(data) {
      // 设备状态变化使用节流日志
      const logKey = `status_change_${data.deviceId}`
      if (!this._statusChangeLogTimes) this._statusChangeLogTimes = {}
      if (!this._statusChangeLogTimes[logKey] || Date.now() - this._statusChangeLogTimes[logKey] > 30000) { // 30秒节流
        console.log('设备信息组件: 设备状态变化', data.deviceId, data.status)
        this._statusChangeLogTimes[logKey] = Date.now()
      }

      // 直接更新store中的设备状态
      if (data.deviceId && data.status) {
        this.$store.dispatch('device/updateDeviceStatus', {
          deviceId: data.deviceId,
          status: data.status,
          ...data
        })
      }
      // 重新加载设备列表以确保同步
      this.loadDevices()
    },

    // 处理设备列表更新
    handleDevicesListUpdate(devices) {
      // 使用节流日志，避免频繁输出设备列表更新信息
      if (!this._deviceUpdateLogTime || Date.now() - this._deviceUpdateLogTime > 30000) { // 30秒节流
        console.log('设备信息组件: 设备列表更新', devices.length, '个设备')
        this._deviceUpdateLogTime = Date.now()
      }

      // 设备去重处理
      const deviceMap = new Map()
      devices.forEach(device => {
        const deviceId = device.deviceId
        if (deviceMap.has(deviceId)) {
          // 如果设备已存在，保留最新的状态信息
          const existing = deviceMap.get(deviceId)
          const newer = device.lastSeen > existing.lastSeen ? device : existing
          deviceMap.set(deviceId, newer)
          // 设备去重日志不需要频繁输出，只在调试时启用
          // console.log(`设备去重: ${deviceId}, 保留较新的设备信息`)
        } else {
          deviceMap.set(deviceId, device)
        }
      })

      const uniqueDevices = Array.from(deviceMap.values())
      console.log(`设备去重完成: 原始${devices.length}个, 去重后${uniqueDevices.length}个`)

      const mappedDevices = uniqueDevices.map(device => ({
        device_id: device.deviceId,
        device_name: device.deviceName,
        device_info: device.deviceInfo,
        status: device.status,
        last_seen: device.lastSeen,
        created_at: device.connectedAt,
        ip_address: device.ipAddress || '未知'
      }))

      // 更新store中的设备列表
      this.$store.commit('device/SET_DEVICES', mappedDevices)
      this.allDevices = mappedDevices
    },

    // 处理设备上线
    handleDeviceOnline(device) {
      console.log('设备信息组件: 设备上线', device)
      // 更新store中的设备状态
      this.$store.dispatch('device/deviceOnline', device)
      this.loadDevices()
    },

    // 处理设备离线
    handleDeviceOffline(device) {
      // 设备离线日志使用节流
      const deviceId = device.deviceId || device
      const logKey = `offline_${deviceId}`
      if (!this._offlineLogTimes) this._offlineLogTimes = {}
      if (!this._offlineLogTimes[logKey] || Date.now() - this._offlineLogTimes[logKey] > 30000) { // 30秒节流
        console.log('设备信息组件: 设备离线', deviceId)
        this._offlineLogTimes[logKey] = Date.now()
      }

      // 更新store中的设备状态
      this.$store.dispatch('device/deviceOffline', deviceId)
      this.loadDevices()
    },

    // 处理设备状态更新
    handleDeviceStatusUpdate(data) {
      // 设备状态更新日志使用节流
      const logKey = `update_${data.deviceId}`
      if (!this._updateLogTimes) this._updateLogTimes = {}
      if (!this._updateLogTimes[logKey] || Date.now() - this._updateLogTimes[logKey] > 30000) { // 30秒节流
        console.log('设备信息组件: 设备状态更新', data.deviceId, data.status)
        this._updateLogTimes[logKey] = Date.now()
      }

      // 更新store中的设备状态
      this.$store.commit('device/UPDATE_DEVICE', {
        deviceId: data.deviceId,
        updates: {
          status: data.status,
          last_seen: data.lastSeen || new Date().toISOString()
        }
      })
      this.loadDevices()
    },

    // 处理设备选择状态变化
    handleDeviceSelectionChanged(data) {
      console.log('🔄 [DeviceInfo] 收到设备选择状态变化事件:', data)

      if (data.reason === 'device_disconnected' && data.removedDevice) {
        console.log(`🔄 [DeviceInfo] 设备 ${data.removedDevice} 因断开连接被移除，强制刷新界面`)

        // 强制重新加载设备列表以更新UI状态
        this.loadDevices()

        // 强制更新组件
        this.$forceUpdate()

        // 延迟一点再次更新，确保状态同步
        this.$nextTick(() => {
          this.$forceUpdate()
        })
      }
    },

    // 更新连接状态
    updateConnectionStatus() {
      if (this.wsManager) {
        const status = this.wsManager.getConnectionStatus()
        this.connectionType = status.isConnected ? 'websocket' : 'disconnected'
        // 连接状态更新日志使用节流
        if (!this._connectionStatusLogTime || Date.now() - this._connectionStatusLogTime > 60000) { // 1分钟节流
          console.log('🔧 [DeviceInfo] 连接状态更新:', {
            isConnected: status.isConnected,
            type: this.connectionType
          })
          this._connectionStatusLogTime = Date.now()
        }
      } else {
        this.connectionType = 'disconnected'
        // WebSocket管理器未初始化的日志也使用节流
        if (!this._wsManagerLogTime || Date.now() - this._wsManagerLogTime > 120000) { // 2分钟节流
          console.log('🔧 [DeviceInfo] WebSocket管理器未初始化，设置为未连接')
          this._wsManagerLogTime = Date.now()
        }
      }
    },

    // 加载设备列表
    async loadDevices() {
      try {
        await this.$store.dispatch('device/fetchDevices')
        // 直接从store获取设备列表，确保状态同步
        this.allDevices = this.$store.getters['device/devices']
        // 设备列表加载日志使用节流
        if (!this._loadDevicesLogTime || Date.now() - this._loadDevicesLogTime > 60000) { // 1分钟节流
          console.log('[DeviceInfo] 设备列表已从store加载:', this.allDevices.length)
          this._loadDevicesLogTime = Date.now()
        }
      } catch (error) {
        console.error('加载设备列表失败:', error)
      }
    },

    // 刷新设备信息
    async refreshDeviceInfo() {
      this.refreshing = true
      try {
        await this.loadDevices()
        this.updateConnectionStatus()
        this.$message.success('设备信息已刷新')
      } catch (error) {
        this.$message.error('刷新失败: ' + error.message)
      } finally {
        this.refreshing = false
      }
    },

    // 选择设备
    selectDevice(device) {
      if (this.enableBatchSelect) {
        // 批量选择模式：切换设备选中状态
        this.toggleDeviceSelection(device, !this.selectedDeviceIds.includes(device.device_id))
      } else {
        // 单选模式：发送设备选择事件
        this.$emit('device-selected', device)
      }
    },

    // 切换设备选中状态
    toggleDeviceSelection(device, checked) {
      const deviceIds = [...this.selectedDeviceIds]
      const index = deviceIds.indexOf(device.device_id)

      if (checked && index === -1) {
        // 🔥 添加设备时检查是否正在执行其他功能
        const deviceExecutionStatus = this.$store.getters['device/getDeviceExecutionStatus'](device.device_id)

        if (this.isDeviceExecutingOtherFunction(device)) {
          this.$message.warning(`设备 ${device.device_name} 正在执行其他脚本 (${deviceExecutionStatus.functionType})，无法选择`)
          return
        }

        // 添加设备
        deviceIds.push(device.device_id)
        this.$emit('device-selected', device) // 发送单个设备选择事件
        console.log(`✅ 设备 ${device.device_name} 已选中`)
      } else if (!checked && index > -1) {
        // 🔥 移除设备时总是允许，无论设备状态如何
        deviceIds.splice(index, 1)
        this.$emit('device-removed', device) // 发送设备移除事件
        console.log(`❌ 设备 ${device.device_name} 已取消选中`)
      }

      // 发送批量选择更新事件
      this.$emit('devices-selection-changed', deviceIds)
    },

    // 全选设备
    selectAllDevices() {
      // 过滤出可选择的设备（在线设备 + 正在执行当前功能的忙碌设备）
      const selectableDeviceIds = this.availableDevices
        .filter(device => this.canSelectDevice(device))
        .map(device => device.device_id)

      if (selectableDeviceIds.length === 0) {
        this.$message.warning('没有可选择的设备（所有设备都在忙碌中）')
        return
      }

      // 发送每个设备的选择事件
      this.availableDevices.forEach(device => {
        if (this.canSelectDevice(device) && !this.selectedDeviceIds.includes(device.device_id)) {
          this.$emit('device-selected', device)
        }
      })

      // 发送批量选择更新事件
      this.$emit('devices-selection-changed', selectableDeviceIds)
      this.$message.success(`已选择所有 ${selectableDeviceIds.length} 个可用设备`)
    },

    // 清空所有选择
    clearAllDevices() {
      // 发送每个设备的移除事件
      this.selectedDeviceIds.forEach(deviceId => {
        const device = this.availableDevices.find(d => d.device_id === deviceId)
        if (device) {
          this.$emit('device-removed', device)
        }
      })

      // 发送批量选择更新事件
      this.$emit('devices-selection-changed', [])
      this.$message.success('已清空所有选择')
    },

    // 显示批量选择对话框
    showOnlineDevicesDialog() {
      // 重置临时选择列表
      this.tempSelectedDevices = []
      // 显示对话框
      this.batchSelectDialogVisible = true
    },

    // 确认批量选择
    confirmBatchSelect() {
      if (this.tempSelectedDevices.length === 0) {
        this.$message.warning('请至少选择一个设备')
        return
      }

      // 验证选择的设备是否有效
      const validSelectedDevices = this.tempSelectedDevices.filter(deviceId => {
        const device = this.availableDevices.find(d => d.device_id === deviceId)
        return device && this.canSelectDevice(device)
      })

      if (validSelectedDevices.length === 0) {
        this.$message.warning('没有有效的设备可添加')
        return
      }

      // 发送每个选择设备的选择事件
      validSelectedDevices.forEach(deviceId => {
        const device = this.availableDevices.find(d => d.device_id === deviceId)
        if (device && !this.selectedDeviceIds.includes(device.device_id)) {
          this.$emit('device-selected', device)
        }
      })

      // 合并当前选择和新选择的设备
      const newSelectedDevices = [...new Set([...this.selectedDeviceIds, ...validSelectedDevices])]

      // 发送批量选择更新事件
      this.$emit('devices-selection-changed', newSelectedDevices)

      // 关闭对话框
      this.batchSelectDialogVisible = false

      // 显示成功消息
      const skippedCount = this.tempSelectedDevices.length - validSelectedDevices.length
      let message = `已添加 ${validSelectedDevices.length} 个设备到执行列表`
      if (skippedCount > 0) {
        message += `（跳过 ${skippedCount} 个无效设备）`
      }
      this.$message.success(message)

      // 清空临时选择
      this.tempSelectedDevices = []
    },

    // 取消批量选择
    cancelBatchSelect() {
      this.batchSelectDialogVisible = false
      this.tempSelectedDevices = []
    },

    // 获取已选设备名称
    getSelectedDeviceNames() {
      const selectedDevices = this.availableDevices.filter(device =>
        this.selectedDeviceIds.includes(device.device_id)
      )
      return selectedDevices.map(device => device.device_name).join(', ')
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 'online':
          return 'success'  // 绿色
        case 'busy':
          return 'warning'  // 橙色
        case 'offline':
          return 'danger'   // 红色
        default:
          return 'info'     // 灰色
      }
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'online':
          return '在线'
        case 'busy':
          return '忙碌'
        case 'offline':
          return '离线'
        default:
          return '未知'
      }
    },

    // 检查设备是否正在执行当前功能
    isDeviceExecutingCurrentFunction(deviceId) {
      if (!this.currentFunction) return false

      // 首先检查父组件的执行设备列表（更准确的实时状态）
      if (this.$parent && this.$parent.executingDevices) {
        const isInExecutingList = this.$parent.executingDevices.some(device =>
          device.deviceId === deviceId
        )

        // 如果设备在执行列表中，且当前功能匹配，则认为正在执行当前功能
        if (isInExecutingList && this.$parent.selectedFunction === this.currentFunction) {
          // 使用节流日志，避免频繁输出
          const logKey = `executing_current_${deviceId}`
          if (!this._executingLogTimes) this._executingLogTimes = {}
          if (!this._executingLogTimes[logKey] || Date.now() - this._executingLogTimes[logKey] > 20000) { // 20秒节流
            console.log(`✅ [DeviceInfo] 设备 ${deviceId} 在执行列表中，当前功能: ${this.currentFunction}`)
            this._executingLogTimes[logKey] = Date.now()
          }
          return true
        }
      }

      // 备选方案：检查Vuex状态
      let deviceTasks = []

      if (this.currentFunction.startsWith('xiaohongshu') ||
          ['profile', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage', 'videoPublish'].includes(this.currentFunction)) {
        // 小红书自动化功能
        deviceTasks = this.$store.getters['xiaohongshu/getDeviceTasks'](deviceId)
      } else if (this.currentFunction.startsWith('xianyu') ||
                 ['keywordMessage'].includes(this.currentFunction)) {
        // 闲鱼自动化功能
        deviceTasks = this.$store.getters['xianyu/getDeviceTasks'](deviceId)
      }

      if (!deviceTasks || deviceTasks.length === 0) return false

      // 检查是否有当前功能的运行任务
      return deviceTasks.some(task => task.functionType === this.currentFunction && task.status === 'running')
    },

    // 检查设备是否可以被选择
    canSelectDevice(device) {
      // 统一使用新的设备执行状态跟踪机制
      const deviceExecutionStatus = this.$store.getters['device/getDeviceExecutionStatus'](device.device_id)

      // 🔥 关键修复：正在执行当前功能的设备应该总是可以选择
      if (deviceExecutionStatus &&
          deviceExecutionStatus.module === 'xiaohongshu' &&
          deviceExecutionStatus.functionType === this.currentFunction) {
        // 使用节流日志，避免频繁输出
        if (!this._lastLogTime || Date.now() - this._lastLogTime > 10000) { // 10秒节流
          console.log(`✅ [DeviceInfo] 设备正在执行当前功能，允许选择:`, {
            deviceId: device.device_id,
            currentFunction: this.currentFunction,
            deviceStatus: device.status
          })
          this._lastLogTime = Date.now()
        }
        return true
      }

      // 在线设备且没有执行任何功能时可以选择
      if (device.status === 'online' && !deviceExecutionStatus) {
        return true
      }

      // 其他情况不可选择
      if (deviceExecutionStatus) {
        // 使用节流日志，避免频繁输出
        const logKey = `device_busy_${device.device_id}`
        if (!this._busyLogTimes) this._busyLogTimes = {}
        if (!this._busyLogTimes[logKey] || Date.now() - this._busyLogTimes[logKey] > 15000) { // 15秒节流
          console.log(`❌ [DeviceInfo] 设备正在执行其他功能，不可选择:`, {
            deviceId: device.device_id,
            executingFunction: deviceExecutionStatus.functionType,
            executingModule: deviceExecutionStatus.module,
            currentFunction: this.currentFunction
          })
          this._busyLogTimes[logKey] = Date.now()
        }
      }

      return false
    },

    // 检查设备是否正在执行其他功能
    isDeviceExecutingOtherFunction(device) {
      const deviceExecutionStatus = this.$store.getters['device/getDeviceExecutionStatus'](device.device_id)

      if (!deviceExecutionStatus) {
        return false
      }

      // 如果设备正在执行其他模块或其他功能，返回true
      return deviceExecutionStatus.module !== 'xiaohongshu' ||
             deviceExecutionStatus.functionType !== this.currentFunction
    },

    // ===== 自动刷新方法 =====

    // 开始自动刷新
    startAutoRefresh() {
      if (!this.isAutoRefreshEnabled) return

      console.log(`[DeviceInfo] 开始自动刷新，间隔: ${this.refreshInterval}ms`)
      this.refreshTimer = setInterval(() => {
        this.autoRefreshDevices()
      }, this.refreshInterval)
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        console.log('[DeviceInfo] 停止自动刷新')
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    // 自动刷新设备信息（静默刷新，不显示loading）
    async autoRefreshDevices() {
      try {
        // 静默刷新，不显示loading状态
        await this.$store.dispatch('device/fetchDevices')
        this.allDevices = this.$store.getters['device/devices']
        this.updateConnectionStatus()

        // 只在控制台输出，不显示用户提示
        console.log('[DeviceInfo] 设备信息自动刷新完成')
      } catch (error) {
        console.error('[DeviceInfo] 自动刷新失败:', error)
        // 刷新失败时不显示错误提示，避免干扰用户
      }
    },

    // 切换自动刷新状态
    toggleAutoRefresh() {
      this.isAutoRefreshEnabled = !this.isAutoRefreshEnabled
      if (this.isAutoRefreshEnabled) {
        this.startAutoRefresh()
        this.$message.success('已开启自动刷新')
      } else {
        this.stopAutoRefresh()
        this.$message.info('已关闭自动刷新')
      }
    },

    // 断开设备
    async disconnectDevice(device) {
      try {
        await this.$confirm(
          `确定要断开设备 "${device.device_name}" 的连接吗？\n\n断开后设备记录将保留为离线状态，可重新连接。`,
          '确认断开连接',
          {
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        )

        console.log('断开设备:', device.device_name, device.device_id)
        await this.$store.dispatch('device/deleteDevice', device.device_id)
        this.$message.success('设备连接已断开，记录已保留为离线状态')

        // 如果断开的是当前选中的设备，清除选择
        if (this.selectedDeviceId === device.device_id) {
          this.$emit('device-selected', null)
        }

        // 刷新设备列表
        setTimeout(() => {
          this.loadDevices()
        }, 1000)

      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('断开失败: ' + error.message)
        }
      }
    },

    // 清理事件监听器
    cleanupEventListeners() {
      if (this.wsManager) {
        this.wsManager.off('connection_established')
        this.wsManager.off('device_status_changed')
        this.wsManager.off('devices_list')
        this.wsManager.off('device_status_update')
      }
    }
  }
}
</script>

<style scoped>
.device-info-component {
  width: 100%;
}

.device-content {
  padding: 0;
}

.no-device {
  text-align: center;
  padding: 20px;
}

.device-list {
  max-height: 300px;
  overflow-y: auto;
}

.device-item {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.device-item:hover {
  border-color: #409EFF;
  background-color: #F0F9FF;
}

.device-item.selected {
  border-color: #409EFF;
  background-color: #E1F3FF;
}

.device-item.batch-mode {
  cursor: pointer;
}

.device-item.batch-mode.selected {
  border-color: #67C23A;
  background-color: #F0F9FF;
  box-shadow: 0 2px 4px rgba(103, 194, 58, 0.2);
}

.device-item.busy {
  border-color: #E6A23C;
  background-color: #FDF6EC;
  opacity: 0.9;
  cursor: pointer; /* 🔥 修复：忙碌设备也可以点击 */
}

.device-item.busy:hover {
  border-color: #E6A23C;
  background-color: #FDF6EC;
}

/* 🔥 新增：正在执行其他功能的设备样式 */
.device-item.executing-other {
  border-color: #F56C6C;
  background-color: #FEF0F0;
  opacity: 0.7;
  cursor: pointer;
}

.device-item.executing-other:hover {
  border-color: #F56C6C;
  background-color: #FEF0F0;
}

.device-item.disabled {
  cursor: not-allowed;
  pointer-events: none;
}

.device-item.disabled .device-name {
  color: #C0C4CC;
}

.batch-device-item.busy {
  border-color: #E6A23C;
  background-color: #FDF6EC;
  opacity: 0.8;
}

.batch-select-info {
  margin-bottom: 10px;
}

/* 批量选择对话框样式 */
.batch-select-dialog {
  max-height: 500px;
}

.device-selection-list {
  max-height: 350px;
  overflow-y: auto;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 10px;
}

.batch-device-item {
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.batch-device-item:hover {
  border-color: #409EFF;
  background-color: #F0F9FF;
}

.batch-device-item.already-selected {
  background-color: #F5F7FA;
  border-color: #C0C4CC;
}

.device-info-row {
  width: 100%;
}

.device-main-info {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.device-main-info .device-name {
  font-weight: bold;
  margin-right: 8px;
}

.device-main-info .el-tag {
  margin-left: 8px;
}

.device-sub-info {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.device-sub-info .device-ip {
  margin-left: 12px;
}

.device-specs {
  font-size: 11px;
  color: #C0C4CC;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-info {
  color: #606266;
  font-size: 14px;
}

.footer-buttons {
  display: flex;
  gap: 8px;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.device-name {
  font-weight: bold;
  color: #303133;
}

.device-details {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.device-id {
  margin-bottom: 2px;
}

.device-specs {
  margin-bottom: 2px;
}

.device-ip {
  color: #909399;
}

.device-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.connection-status {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #EBEEF5;
  font-size: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.status-label {
  color: #909399;
}

.status-value {
  color: #303133;
  font-weight: bold;
}

.status-connected {
  color: #67C23A;
  font-weight: bold;
}

.status-polling {
  color: #E6A23C;
  font-weight: bold;
}

.status-disconnected {
  color: #F56C6C;
  font-weight: bold;
}
</style>
