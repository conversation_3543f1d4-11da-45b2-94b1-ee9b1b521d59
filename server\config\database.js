const mysql = require('mysql2/promise');

// 本地数据库配置（现有系统）
const localDbConfig = {
  host: 'localhost',
  user: 'autojs_control',
  password: 'root',
  database: 'autojs_control',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 主站数据库配置（用于账号验证）
const mainDbConfig = {
  host: process.env.MAIN_DB_HOST || 'localhost',
  port: process.env.MAIN_DB_PORT || 3306,
  user: process.env.MAIN_DB_USER || 'zhuzhan',
  password: process.env.MAIN_DB_PASSWORD || 'root',
  database: process.env.MAIN_DB_NAME || 'zhuzhan',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建本地数据库连接池
const localPool = mysql.createPool({
  ...localDbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 创建主站数据库连接池
const mainPool = mysql.createPool({
  ...mainDbConfig,
  waitForConnections: true,
  connectionLimit: 5,
  queueLimit: 0
});

// 向后兼容：保持原有的pool变量指向本地数据库
const pool = localPool;

// 初始化数据库表
async function initDatabase() {
  try {
    const connection = await pool.getConnection();

    // 创建数据库（如果不存在）
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${dbConfig.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);

    // 释放连接并重新连接到指定数据库
    connection.release();
    const dbConnection = await pool.getConnection();

    // 用户表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 设备表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS devices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) UNIQUE NOT NULL,
        device_name VARCHAR(100) NOT NULL,
        device_info JSON,
        status ENUM('online', 'offline', 'busy') DEFAULT 'offline',
        last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 设备应用信息表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS device_apps (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) NOT NULL,
        app_type ENUM('xiaohongshu', 'xianyu') NOT NULL,
        app_name VARCHAR(200) NOT NULL,
        app_text VARCHAR(500) NOT NULL,
        app_bounds VARCHAR(200),
        is_clickable BOOLEAN DEFAULT FALSE,
        detection_method VARCHAR(50) NOT NULL,
        detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_device_id (device_id),
        INDEX idx_app_type (app_type),
        INDEX idx_detected_at (detected_at)
      )
    `);

    // 脚本表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS scripts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        content LONGTEXT NOT NULL,
        version VARCHAR(20) DEFAULT '1.0.0',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 执行日志表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS execution_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) NOT NULL,
        script_id INT,
        command TEXT NOT NULL,
        result TEXT,
        status ENUM('pending', 'running', 'success', 'error') DEFAULT 'pending',
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        INDEX idx_device_id (device_id),
        INDEX idx_script_id (script_id),
        FOREIGN KEY (script_id) REFERENCES scripts(id) ON DELETE SET NULL
      )
    `);

    // 文件传输记录表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS file_transfers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) NOT NULL,
        filename VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT DEFAULT 0,
        transfer_type ENUM('upload', 'download') NOT NULL,
        status ENUM('pending', 'transferring', 'completed', 'failed') DEFAULT 'pending',
        video_filename VARCHAR(255) DEFAULT '' COMMENT '视频文件名',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        INDEX idx_device_id (device_id),
        INDEX idx_video_filename (video_filename)
      )
    `);

    // 小红书自动化执行日志表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS xiaohongshu_execution_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id VARCHAR(100) UNIQUE NOT NULL,
        function_type ENUM('profile', 'groupChat', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage') NOT NULL,
        device_id VARCHAR(100) NOT NULL,
        device_name VARCHAR(100) NOT NULL,
        config_params JSON NOT NULL,
        schedule_config JSON,
        execution_status ENUM('pending', 'running', 'completed', 'failed', 'stopped') DEFAULT 'pending',
        progress_percentage INT DEFAULT 0,
        execution_result TEXT,
        execution_logs LONGTEXT,
        error_message TEXT,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        execution_duration INT DEFAULT 0,
        INDEX idx_task_id (task_id),
        INDEX idx_device_id (device_id),
        INDEX idx_function_type (function_type),
        INDEX idx_execution_status (execution_status),
        INDEX idx_started_at (started_at)
      )
    `);

    // 闲鱼自动化执行日志表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS xianyu_execution_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id VARCHAR(100) UNIQUE NOT NULL,
        function_type ENUM('keywordMessage') NOT NULL,
        device_id VARCHAR(100) NOT NULL,
        device_name VARCHAR(100) NOT NULL,
        function_name VARCHAR(100) NOT NULL,
        config_params JSON NOT NULL,
        schedule_config JSON,
        execution_status ENUM('pending', 'running', 'completed', 'failed', 'stopped') DEFAULT 'pending',
        progress_percentage INT DEFAULT 0,
        execution_result TEXT,
        execution_logs LONGTEXT,
        error_message TEXT,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        execution_duration INT DEFAULT 0,
        user_id INT NOT NULL DEFAULT 1,
        INDEX idx_task_id (task_id),
        INDEX idx_device_id (device_id),
        INDEX idx_function_type (function_type),
        INDEX idx_execution_status (execution_status),
        INDEX idx_started_at (started_at),
        INDEX idx_user_id (user_id)
      )
    `);

    // 检查并添加user_id字段（如果表已存在但缺少该字段）
    try {
      await dbConnection.execute(`
        ALTER TABLE xianyu_execution_logs
        ADD COLUMN user_id INT NOT NULL DEFAULT 1
      `);
      console.log('✅ 已为xianyu_execution_logs表添加user_id字段');
    } catch (alterError) {
      // 如果字段已存在，忽略错误
      if (!alterError.message.includes('Duplicate column name')) {
        console.log('⚠️ 添加user_id字段时出现错误（可能字段已存在）:', alterError.message);
      }
    }

    // 闲鱼私聊记录表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS xianyu_chat_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        device_id VARCHAR(100) NOT NULL,
        device_name VARCHAR(100) NOT NULL,
        keyword VARCHAR(200) NOT NULL,
        post_title VARCHAR(500),
        post_price VARCHAR(100),
        post_location VARCHAR(200),
        seller_name VARCHAR(200),
        message_content TEXT NOT NULL,
        chat_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        post_url VARCHAR(1000),
        post_id VARCHAR(200),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_device_id (device_id),
        INDEX idx_keyword (keyword),
        INDEX idx_chat_time (chat_time),
        INDEX idx_created_at (created_at)
      )
    `);

    // UID文件管理表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS uid_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT DEFAULT 0,
        total_uid_count INT DEFAULT 0,
        uploaded_by VARCHAR(100) NOT NULL,
        upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'deleted') DEFAULT 'active',
        INDEX idx_file_name (file_name),
        INDEX idx_uploaded_by (uploaded_by),
        INDEX idx_upload_time (upload_time),
        INDEX idx_status (status)
      )
    `);

    // UID数据表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS uid_data (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_id INT NOT NULL,
        uid VARCHAR(100) NOT NULL,
        is_used BOOLEAN DEFAULT FALSE,
        used_time TIMESTAMP NULL,
        used_device_id VARCHAR(100) NULL,
        used_device_name VARCHAR(100) NULL,
        task_id VARCHAR(100) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_file_id (file_id),
        INDEX idx_uid (uid),
        INDEX idx_is_used (is_used),
        INDEX idx_used_device_id (used_device_id),
        INDEX idx_task_id (task_id),
        FOREIGN KEY (file_id) REFERENCES uid_files(id) ON DELETE CASCADE
      )
    `);

    // 小红书视频文件管理表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS xiaohongshu_video_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size BIGINT DEFAULT 0,
        file_hash VARCHAR(32) DEFAULT '' COMMENT '文件MD5哈希值',
        video_duration INT DEFAULT 0 COMMENT '视频时长(秒)',
        video_format VARCHAR(20) DEFAULT '' COMMENT '视频格式(mp4, avi等)',
        video_resolution VARCHAR(20) DEFAULT '' COMMENT '视频分辨率',
        thumbnail_path VARCHAR(500) DEFAULT '' COMMENT '缩略图路径',
        uploaded_by VARCHAR(100) NOT NULL,
        upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('active', 'deleted') DEFAULT 'active',
        description TEXT COMMENT '视频描述',
        tags VARCHAR(500) DEFAULT '' COMMENT '视频标签',
        transfer_count INT DEFAULT 0 COMMENT '传输次数',
        transferred_devices JSON COMMENT '已传输的设备列表',
        last_transfer_time TIMESTAMP NULL COMMENT '最后传输时间',
        INDEX idx_file_name (file_name),
        INDEX idx_uploaded_by (uploaded_by),
        INDEX idx_upload_time (upload_time),
        INDEX idx_status (status),
        INDEX idx_video_format (video_format),
        INDEX idx_file_hash (file_hash),
        INDEX idx_transfer_count (transfer_count),
        UNIQUE KEY unique_file_hash (file_hash, file_size)
      )
    `);

    // 小红书视频传输记录表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS xiaohongshu_video_transfers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT NOT NULL,
        device_id VARCHAR(100) NOT NULL,
        device_name VARCHAR(100) NOT NULL,
        transfer_type ENUM('manual', 'script_execution') DEFAULT 'manual' COMMENT '传输类型：手动传输或脚本执行传输',
        task_id VARCHAR(100) DEFAULT '' COMMENT '关联的任务ID',
        transfer_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('pending', 'transferring', 'completed', 'failed') DEFAULT 'pending',
        transfer_progress INT DEFAULT 0 COMMENT '传输进度百分比',
        error_message TEXT COMMENT '错误信息',
        completed_time TIMESTAMP NULL,
        file_size BIGINT DEFAULT 0 COMMENT '传输文件大小',
        transfer_speed DECIMAL(10,2) DEFAULT 0 COMMENT '传输速度(KB/s)',
        video_filename VARCHAR(255) DEFAULT '' COMMENT '视频文件名',
        INDEX idx_video_id (video_id),
        INDEX idx_device_id (device_id),
        INDEX idx_task_id (task_id),
        INDEX idx_status (status),
        INDEX idx_transfer_time (transfer_time),
        INDEX idx_transfer_type (transfer_type),
        INDEX idx_video_filename (video_filename),
        FOREIGN KEY (video_id) REFERENCES xiaohongshu_video_files(id) ON DELETE CASCADE
      )
    `);

    // 小红书视频分配记录表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS xiaohongshu_video_assignments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        video_id INT NOT NULL,
        device_id VARCHAR(100) NOT NULL,
        device_name VARCHAR(100) NOT NULL,
        task_id VARCHAR(100) NOT NULL,
        assignment_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        status ENUM('assigned', 'uploading', 'completed', 'failed') DEFAULT 'assigned',
        upload_progress INT DEFAULT 0 COMMENT '上传进度百分比',
        error_message TEXT COMMENT '错误信息',
        completed_time TIMESTAMP NULL,
        video_title VARCHAR(200) DEFAULT '' COMMENT '发布时的视频标题',
        video_description TEXT COMMENT '发布时的视频描述',
        publish_result JSON COMMENT '发布结果详情',
        INDEX idx_video_id (video_id),
        INDEX idx_device_id (device_id),
        INDEX idx_task_id (task_id),
        INDEX idx_status (status),
        INDEX idx_assignment_time (assignment_time),
        FOREIGN KEY (video_id) REFERENCES xiaohongshu_video_files(id) ON DELETE CASCADE
      )
    `);

    // 小红书视频发布执行日志表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS xiaohongshu_video_execution_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id VARCHAR(100) NOT NULL,
        device_id VARCHAR(100) NOT NULL,
        device_name VARCHAR(100) NOT NULL,
        function_type VARCHAR(50) DEFAULT 'videoPublish',
        execution_status ENUM('waiting', 'running', 'completed', 'failed', 'stopped') DEFAULT 'waiting',
        config_data JSON COMMENT '执行配置数据',
        started_at TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        execution_duration INT DEFAULT 0 COMMENT '执行时长(秒)',
        progress_percentage INT DEFAULT 0,
        current_step VARCHAR(200) DEFAULT '',
        result_data JSON COMMENT '执行结果数据',
        error_message TEXT,
        video_count INT DEFAULT 0 COMMENT '分配的视频数量',
        published_count INT DEFAULT 0 COMMENT '成功发布的视频数量',
        failed_count INT DEFAULT 0 COMMENT '发布失败的视频数量',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_task_id (task_id),
        INDEX idx_device_id (device_id),
        INDEX idx_function_type (function_type),
        INDEX idx_execution_status (execution_status),
        INDEX idx_started_at (started_at)
      )
    `);

    // 创建默认管理员用户
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash('admin123', 10);

    await dbConnection.execute(`
      INSERT IGNORE INTO users (username, password, email)
      VALUES ('admin', ?, '<EMAIL>')
    `, [hashedPassword]);

    dbConnection.release();
    console.log('数据库初始化完成');

  } catch (error) {
    console.error('数据库初始化失败:', error);
  }
}

// 数据库连接测试函数
async function testDatabaseConnections() {
  console.log('🔍 测试数据库连接...');

  try {
    // 测试本地数据库连接
    const localConnection = await localPool.getConnection();
    console.log('✅ 本地数据库连接成功');
    localConnection.release();
  } catch (error) {
    console.error('❌ 本地数据库连接失败:', error.message);
  }

  try {
    // 测试主站数据库连接
    const mainConnection = await mainPool.getConnection();
    console.log('✅ 主站数据库连接成功');
    mainConnection.release();
  } catch (error) {
    console.error('❌ 主站数据库连接失败:', error.message);
    console.log('💡 提示：如果主站数据库未配置，这是正常的。系统将使用本地认证模式。');
  }
}

// 导出数据库连接池和配置
module.exports = {
  // 向后兼容
  pool: localPool,
  initDatabase,
  dbConfig: localDbConfig,

  // 新的双数据库支持
  localPool,
  mainPool,

  // 配置信息
  localDbConfig,
  mainDbConfig,

  // 工具函数
  testDatabaseConnections
};
