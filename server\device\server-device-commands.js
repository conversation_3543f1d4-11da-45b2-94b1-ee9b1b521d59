/**
 * 服务器设备命令管理模块 - 完整拆分版本
 * 包含所有设备命令管理相关的API和功能
 * 对应原始文件第577-1577行和其他设备命令相关的完整内容，包含以下API：
 * - POST /api/device/register - 设备注册
 * - GET /api/device/:deviceId/commands - 获取设备命令
 * - POST /api/device/:deviceId/result - 设备结果上报
 * - POST /api/device/:deviceId/disconnect - 设备主动断开
 * - POST /api/device/force-status - 强制更新设备状态
 * - GET /api/device/check-stop - 检查停止状态
 * - POST /api/device/script-stopped - 脚本停止通知
 * 以及所有相关的设备命令管理功能
 */

// 设备命令管理模块设置函数
async function setupServerDeviceCommands(app, io, coreData, authData) {
  console.log('🔧 设置设备命令管理模块...');

  const {
    pool,
    devices,
    webClients,
    logs,
    pendingCommands,
    deviceCommands,
    throttledLog,
    xiaohongshuLogService,
    xianyuLogService,
    cleanupXiaohongshuTasksForDevice,
    cleanupXianyuTasksForDevice
  } = coreData;

  const { authenticateToken } = authData;

  // 引入设备认证中间件
  const { authenticateDevice, verifyDeviceOwnership } = require('../middleware/deviceAuth');

  // 设备注册API (原始文件第577行) - 已更新为连接码优先逻辑
  app.post('/api/device/register', async (req, res) => {
    const { deviceId, deviceName, deviceInfo, deviceIP, connectionCode } = req.body;

    console.log('设备注册请求:', { deviceId, deviceName, deviceIP, connectionCode: connectionCode ? '***' : '无' });

    if (!deviceId || !deviceName) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：设备ID和设备名称'
      });
    }

    if (!pool) {
      return res.status(500).json({
        success: false,
        message: '数据库连接不可用'
      });
    }

    let userId = null;
    let username = null;
    let isReconnection = false;

    try {
      // 首先检查设备是否已经注册过
      console.log(`[设备注册] 检查设备${deviceId}是否已注册`);
      const [existingDevices] = await pool.execute(
        'SELECT device_id, user_id FROM devices WHERE device_id = ? AND user_id IS NOT NULL',
        [deviceId]
      );

      if (existingDevices.length > 0) {
        // 设备已注册，允许重连
        const existingDevice = existingDevices[0];
        userId = existingDevice.user_id;
        isReconnection = true;

        // 获取用户信息
        const [users] = await pool.execute(
          'SELECT username FROM users WHERE id = ?',
          [userId]
        );

        if (users.length > 0) {
          username = users[0].username;
          console.log(`[设备重连] 设备${deviceId}重新连接，保持原有分配: 用户${userId}(${username})`);
        } else {
          console.log(`[设备重连] 警告：设备${deviceId}的用户${userId}不存在，需要重新分配`);
          isReconnection = false;
        }
      }

      // 如果不是重连，则必须提供连接码
      if (!isReconnection) {
        if (!connectionCode) {
          return res.status(400).json({
            success: false,
            message: '新设备首次连接必须提供连接码'
          });
        }

        console.log(`[设备注册] 新设备${deviceId}使用连接码注册: ${connectionCode}`);

        // 验证连接码
        const [codes] = await pool.execute(`
          SELECT id, user_id, username, max_devices, used_count, expires_at, is_active
          FROM device_connection_codes
          WHERE code = ?
        `, [connectionCode]);

        if (codes.length === 0) {
          return res.status(404).json({
            success: false,
            message: '连接码不存在'
          });
        }

        const connectionCodeData = codes[0];

        // 检查连接码是否激活
        if (!connectionCodeData.is_active) {
          return res.status(400).json({
            success: false,
            message: '连接码已禁用'
          });
        }

        // 检查是否过期
        if (connectionCodeData.expires_at && new Date() > new Date(connectionCodeData.expires_at)) {
          return res.status(400).json({
            success: false,
            message: '连接码已过期'
          });
        }

        // 检查使用次数限制
        if (connectionCodeData.used_count >= connectionCodeData.max_devices) {
          return res.status(400).json({
            success: false,
            message: '连接码使用次数已达上限'
          });
        }

        // 检查设备是否已经使用过此连接码
        const [existingConnections] = await pool.execute(
          'SELECT id FROM device_connections WHERE device_id = ? AND connection_code = ?',
          [deviceId, connectionCode]
        );

        if (existingConnections.length === 0) {
          // 记录设备连接
          await pool.execute(`
            INSERT INTO device_connections
            (device_id, connection_code, user_id, device_name, device_info)
            VALUES (?, ?, ?, ?, ?)
          `, [deviceId, connectionCode, connectionCodeData.user_id, deviceName, JSON.stringify(deviceInfo || {})]);

          // 更新连接码使用次数
          await pool.execute(
            'UPDATE device_connection_codes SET used_count = used_count + 1 WHERE code = ?',
            [connectionCode]
          );
        }

        userId = connectionCodeData.user_id;
        username = connectionCodeData.username;
        console.log(`[设备注册] 连接码验证成功，设备${deviceId}分配给用户${userId}(${username})`);
      }

      // 保存设备信息到数据库
      console.log(`[设备注册] 用户${userId}注册设备: ${deviceId} (${deviceName})`);

      try {
        // 使用INSERT ON DUPLICATE KEY UPDATE保存设备信息
        await pool.execute(`
          INSERT INTO devices (device_id, device_name, device_info, status, last_seen, user_id, created_at)
          VALUES (?, ?, ?, ?, NOW(), ?, NOW())
          ON DUPLICATE KEY UPDATE
          device_name = VALUES(device_name),
          device_info = VALUES(device_info),
          status = VALUES(status),
          last_seen = NOW(),
          user_id = COALESCE(user_id, VALUES(user_id))
        `, [
          deviceId,
          deviceName,
          JSON.stringify({
            ...deviceInfo,
            ipAddress: deviceIP,
            registrationTime: new Date().toISOString(),
            connectionType: 'http'
          }),
          'online',
          userId
        ]);

        console.log(`✅ 设备信息已保存到数据库: ${deviceId} (用户${userId})`);
      } catch (dbError) {
        console.error('保存设备信息失败:', dbError);
        // 数据库错误不影响注册流程
      }

      // 更新内存中的设备信息
      const deviceData = {
        deviceId,
        deviceName,
        deviceInfo: deviceInfo || {},
        status: 'online',
        connectedAt: new Date(),
        lastSeen: new Date(),
        deviceIP: deviceIP || 'unknown'
      };

      // 查找现有的socket连接
      let existingSocketId = null;
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          existingSocketId = socketId;
          break;
        }
      }

      if (existingSocketId) {
        // 更新现有连接
        devices.set(existingSocketId, { ...devices.get(existingSocketId), ...deviceData });
        console.log(`更新现有设备连接: ${deviceId}`);
      } else {
        // 创建HTTP连接标识
        const httpSocketId = `http_${deviceId}_${Date.now()}`;
        devices.set(httpSocketId, deviceData);
        console.log(`创建HTTP设备连接: ${deviceId}`);
      }

      // 初始化命令队列
      if (!pendingCommands.has(deviceId)) {
        pendingCommands.set(deviceId, []);
      }

      // 通知前端设备上线
      io.emit('device_connected', {
        deviceId,
        deviceName,
        deviceInfo: deviceInfo || {},
        connectedAt: new Date(),
        connectionType: 'http'
      });

      console.log(`设备注册成功: ${deviceId} (${deviceName})`);

      res.json({
        success: true,
        message: '设备注册成功',
        data: {
          deviceId,
          deviceName,
          status: 'online',
          registeredAt: new Date()
        }
      });

    } catch (error) {
      console.error('设备注册失败:', error);
      res.status(500).json({
        success: false,
        message: '设备注册失败: ' + error.message
      });
    }
  });

  // 引入用户隔离中间件和工具
  const { userIsolationMiddleware } = require('../middleware/userIsolation');
  const DatabaseQueryEnhancer = require('../utils/DatabaseQueryEnhancer');
  const PermissionValidator = require('../utils/PermissionValidator');

  // 创建数据库查询增强器和权限验证器
  const dbEnhancer = new DatabaseQueryEnhancer(pool);
  const permissionValidator = new PermissionValidator(pool);

  // 获取设备命令API (原始文件第1200行) - 改为设备认证模式
  app.get('/api/device/:deviceId/commands', authenticateDevice(pool), async (req, res) => {
    const { deviceId } = req.params;
    const { userId, username } = req.device; // 来自设备认证中间件

    try {


      // 获取该设备的待执行命令
      const commands = pendingCommands.get(deviceId) || [];

      // 使用节流日志，避免频繁输出命令查询结果
      const logKey = `device_commands_${deviceId}`;
      throttledLog(logKey, `设备 ${deviceId} 查询命令，当前队列长度: ${commands.length}`);

      if (commands.length > 0) {
        // 返回第一个命令并从队列中移除
        const command = commands.shift();
        pendingCommands.set(deviceId, commands);

        console.log(`返回命令给设备 ${deviceId}:`, JSON.stringify(command, null, 2));

        // 🔥 关键修复：设备获取到脚本命令时，更新状态为忙碌并更新执行日志状态为'running'
        if (command.script && command.script !== 'DISCONNECT_COMMAND') {
          // 更新设备状态为忙碌
          for (const [socketId, device] of devices) {
            if (device.deviceId === deviceId) {
              device.status = 'busy';
              device.lastSeen = new Date();
              console.log(`设备 ${deviceId} 获取到脚本命令，状态更新为忙碌`);
              break;
            }
          }

          // 同时更新数据库状态
          if (pool) {
            try {
              await pool.execute(`
                UPDATE devices SET status = 'busy', last_seen = NOW()
                WHERE device_id = ?
              `, [deviceId]);
              console.log(`数据库中设备状态已更新为忙碌: ${deviceId}`);
            } catch (dbError) {
              console.error('更新数据库设备状态失败:', dbError);
            }
          }

          // 🔥 关键修复：更新执行日志状态为'running'
          console.log(`🔍 [状态修复调试] 检查命令字段:`, {
            hasLogId: !!command.logId,
            logId: command.logId,
            commandKeys: Object.keys(command)
          });

          if (command.logId) {
            try {
              console.log(`🔄 [状态修复] 开始更新执行日志状态为running: ${command.logId}`);

              // 根据logId判断是小红书还是闲鱼任务
              if (command.logId.includes('xiaohongshu_') || command.logId.includes('xhs_')) {
                // 小红书任务
                const xiaohongshuLogService = require('../services/xiaohongshuLogService');
                if (xiaohongshuLogService) {
                  await xiaohongshuLogService.updateExecutionStatus(
                    command.logId,
                    'running',
                    10,
                    '脚本开始执行',
                    `设备 ${deviceId} 开始执行脚本`
                  );
                  console.log(`✅ [状态修复] 小红书执行日志状态已更新为running: ${command.logId}`);
                }
              } else if (command.logId.includes('xianyu_')) {
                // 闲鱼任务
                const xianyuLogService = require('../services/xianyuLogService');
                if (xianyuLogService) {
                  await xianyuLogService.updateExecutionStatus(
                    command.logId,
                    'running',
                    10,
                    '脚本开始执行',
                    `设备 ${deviceId} 开始执行脚本`
                  );
                  console.log(`✅ [状态修复] 闲鱼执行日志状态已更新为running: ${command.logId}`);
                }
              } else {
                console.warn(`⚠️ [状态修复] 无法识别任务类型: ${command.logId}`);
              }
            } catch (error) {
              console.error(`❌ [状态修复] 更新执行日志状态失败: ${command.logId}`, error);
            }
          } else {
            console.warn(`⚠️ [状态修复] 跳过状态更新 - logId: ${command.logId}`);

            // 🔥 强制修复：如果没有logId，尝试从设备状态推断并更新
            if (!command.logId && pool) {
              console.log(`🔧 [强制修复] 尝试查找设备的待执行任务并更新状态`);
              try {
                // 查找该设备最近的pending状态记录（小红书）
                const [xiaohongshuTasks] = await pool.execute(`
                  SELECT task_id FROM xiaohongshu_execution_logs
                  WHERE device_id = ? AND execution_status = 'pending'
                  ORDER BY started_at DESC LIMIT 1
                `, [deviceId]);

                // 查找该设备最近的pending状态记录（闲鱼）
                const [xianyuTasks] = await pool.execute(`
                  SELECT task_id FROM xianyu_execution_logs
                  WHERE device_id = ? AND execution_status = 'pending'
                  ORDER BY started_at DESC LIMIT 1
                `, [deviceId]);

                if (xiaohongshuTasks.length > 0) {
                  const taskId = xiaohongshuTasks[0].task_id;
                  console.log(`🔧 [强制修复] 找到小红书待执行任务，更新状态: ${taskId}`);

                  const xiaohongshuLogService = require('../services/xiaohongshuLogService');
                  if (xiaohongshuLogService) {
                    await xiaohongshuLogService.updateExecutionStatus(
                      taskId,
                      'running',
                      10,
                      '脚本开始执行',
                      `设备 ${deviceId} 开始执行脚本`
                    );
                    console.log(`✅ [强制修复] 小红书执行日志状态已更新为running: ${taskId}`);
                  }
                } else if (xianyuTasks.length > 0) {
                  const taskId = xianyuTasks[0].task_id;
                  console.log(`🔧 [强制修复] 找到闲鱼待执行任务，更新状态: ${taskId}`);

                  const xianyuLogService = require('../services/xianyuLogService');
                  if (xianyuLogService) {
                    await xianyuLogService.updateExecutionStatus(
                      taskId,
                      'running',
                      10,
                      '脚本开始执行',
                      `设备 ${deviceId} 开始执行脚本`
                    );
                    console.log(`✅ [强制修复] 闲鱼执行日志状态已更新为running: ${taskId}`);
                  }
                } else {
                  console.warn(`⚠️ [强制修复] 未找到设备 ${deviceId} 的待执行任务`);
                }
              } catch (error) {
                console.error(`❌ [强制修复] 强制状态更新失败:`, error);
              }
            }
          }
        }

        res.json({
          success: true,
          data: command
        });
      } else {
        // 没有待执行的命令
        res.json({
          success: true,
          data: null
        });
      }

    } catch (error) {
      console.error(`获取设备 ${deviceId} 命令失败:`, error);
      res.status(500).json({
        success: false,
        message: '获取命令失败: ' + error.message
      });
    }
  });

  // 设备结果上报API (原始文件第1421行)
  app.post('/api/device/:deviceId/result', authenticateToken, async (req, res) => {
    const { deviceId } = req.params;
    const { logId, result, status } = req.body;

    console.log(`设备 ${deviceId} 上报执行结果:`, { logId, result, status });

    try {
      // 更新设备最后活跃时间
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          device.lastSeen = new Date();
          devices.set(socketId, device);
          break;
        }
      }

      // 如果有数据库连接，更新执行日志
      if (pool && logId) {
        try {
          // 根据logId判断是小红书还是闲鱼任务，更新对应的数据库表
          if (logId.includes('xiaohongshu_') || logId.includes('xhs_')) {
            // 小红书任务
            await pool.execute(`
              UPDATE xiaohongshu_execution_logs
              SET execution_result = ?, execution_status = ?, completed_at = NOW()
              WHERE id = ?
            `, [result, status, logId]);
            console.log(`小红书执行日志已更新: logId=${logId}, status=${status}`);
          } else if (logId.includes('xianyu_')) {
            // 闲鱼任务
            await pool.execute(`
              UPDATE xianyu_execution_logs
              SET execution_result = ?, execution_status = ?, completed_at = NOW()
              WHERE id = ?
            `, [result, status, logId]);
            console.log(`闲鱼执行日志已更新: logId=${logId}, status=${status}`);
          } else {
            console.warn(`无法识别任务类型，跳过数据库更新: logId=${logId}`);
          }
        } catch (dbError) {
          console.error('更新执行日志失败:', dbError);
        }
      }

      // 通知前端执行结果
      io.emit('execution_result', {
        deviceId,
        logId,
        result,
        status,
        timestamp: new Date()
      });

      res.json({
        success: true,
        message: '执行结果已接收'
      });

    } catch (error) {
      console.error(`处理设备 ${deviceId} 执行结果失败:`, error);
      res.status(500).json({
        success: false,
        message: '处理执行结果失败: ' + error.message
      });
    }
  });

  // 强制更新设备状态API (原始文件第439行)
  app.post('/api/device/force-status', authenticateToken, async (req, res) => {
    try {
      const { deviceId, status } = req.body;

      if (!deviceId || !status) {
        return res.status(400).json({
          success: false,
          message: '缺少必要参数'
        });
      }

      console.log(`强制更新设备状态: ${deviceId} -> ${status}`);

      // 1. 更新内存中的设备状态
      let deviceFound = false;
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          device.status = status;
          device.lastSeen = new Date();
          devices.set(socketId, device);
          deviceFound = true;
          console.log(`内存中设备状态已更新: ${deviceId} -> ${status}`);
          break;
        }
      }

      if (!deviceFound) {
        console.log(`设备 ${deviceId} 在内存中不存在，跳过内存更新`);
      }

      // 2. 更新数据库中的设备状态（如果数据库可用）
      if (pool) {
        try {
          const [result] = await pool.execute(`
            UPDATE devices SET status = ?, last_seen = NOW()
            WHERE device_id = ?
          `, [status, deviceId]);

          if (result.affectedRows > 0) {
            console.log(`数据库中设备状态已更新: ${deviceId} -> ${status}`);
          } else {
            console.log(`设备 ${deviceId} 在数据库中不存在`);
          }
        } catch (dbError) {
          console.error('数据库更新设备状态失败:', dbError);
          // 数据库更新失败不影响响应，因为内存已更新
        }
      }

      // 3. 通过WebSocket广播设备状态更新
      io.emit('device_status_update', {
        deviceId: deviceId,
        status: status,
        lastSeen: new Date()
      });

      console.log(`设备状态强制更新完成: ${deviceId} -> ${status}`);

      res.json({
        success: true,
        message: '设备状态更新成功',
        data: {
          deviceId: deviceId,
          status: status,
          timestamp: new Date()
        }
      });

    } catch (error) {
      console.error('强制更新设备状态失败:', error);
      res.status(500).json({
        success: false,
        message: '更新设备状态失败: ' + error.message
      });
    }
  });

  // 检查停止状态API (原始文件第4507行)
  app.get('/api/device/check-stop', (req, res) => {
    try {
      const { deviceId } = req.query;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      // 检查设备是否有停止命令
      const commands = pendingCommands.get(deviceId) || [];
      const hasStopCommand = commands.some(cmd => cmd.type === 'stop_script' || cmd.type === 'stop');

      console.log(`设备 ${deviceId} 检查停止状态: ${hasStopCommand ? '有停止命令' : '无停止命令'}`);

      res.json({
        success: true,
        shouldStop: hasStopCommand,
        deviceId: deviceId,
        timestamp: new Date()
      });

    } catch (error) {
      console.error('检查停止状态失败:', error);
      res.status(500).json({
        success: false,
        message: '检查停止状态失败: ' + error.message
      });
    }
  });

  // 脚本停止通知API (原始文件第4536行)
  app.post('/api/device/script-stopped', (req, res) => {
    try {
      const { deviceId, timestamp } = req.body;

      if (!deviceId) {
        return res.status(400).json({
          success: false,
          message: '缺少设备ID参数'
        });
      }

      console.log(`设备 ${deviceId} 报告脚本已停止，时间: ${timestamp}`);

      // 更新设备状态
      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          device.status = 'online'; // 从忙碌状态恢复到在线状态
          device.lastSeen = new Date();
          devices.set(socketId, device);
          break;
        }
      }

      // 清除该设备的停止命令
      const commands = pendingCommands.get(deviceId) || [];
      const filteredCommands = commands.filter(cmd =>
        cmd.type !== 'stop_script' && cmd.type !== 'stop'
      );
      pendingCommands.set(deviceId, filteredCommands);

      // 通知前端脚本已停止
      io.emit('script_stopped', {
        deviceId,
        timestamp: timestamp || new Date(),
        status: 'stopped'
      });

      res.json({
        success: true,
        message: '脚本停止通知已接收'
      });

    } catch (error) {
      console.error('处理脚本停止通知失败:', error);
      res.status(500).json({
        success: false,
        message: '处理脚本停止通知失败: ' + error.message
      });
    }
  });

  // 设备主动断开API (原始文件第10530行) - 改为设备认证模式
  app.post('/api/device/:deviceId/disconnect', authenticateDevice(pool), async (req, res) => {
    const { deviceId } = req.params;
    const { userId, username } = req.device; // 来自设备认证中间件
    console.log(`收到设备主动断开请求: ${deviceId} (用户${userId})`);

    try {
      // 1. 清理该设备的所有执行状态（在移除设备之前）
      console.log(`设备 ${deviceId} 主动断开，开始清理相关状态`);

      // 清理小红书任务状态
      if (xiaohongshuLogService) {
        try {
          await cleanupXiaohongshuTasksForDevice(deviceId);
        } catch (cleanupError) {
          console.error(`清理设备 ${deviceId} 小红书任务失败:`, cleanupError);
        }
      }

      // 清理闲鱼任务状态
      if (xianyuLogService) {
        try {
          await cleanupXianyuTasksForDevice(deviceId);
        } catch (cleanupError) {
          console.error(`清理设备 ${deviceId} 闲鱼任务失败:`, cleanupError);
        }
      }

      // 2. 查找并移除设备连接
      let deviceFound = false;
      let deviceName = '';

      for (const [socketId, device] of devices) {
        if (device.deviceId === deviceId) {
          deviceName = device.deviceName;
          devices.delete(socketId);
          deviceFound = true;
          console.log(`已从内存中移除设备: ${deviceId} (${deviceName})`);
          break;
        }
      }

      // 3. 清理命令队列
      if (pendingCommands.has(deviceId)) {
        pendingCommands.delete(deviceId);
        console.log(`已清理设备 ${deviceId} 的命令队列`);
      }

      // 更新数据库状态 - 使用用户隔离
      if (pool) {
        try {
          await pool.execute(`
            UPDATE devices SET status = 'offline', last_seen = NOW()
            WHERE device_id = ? AND user_id = ?
          `, [deviceId, userId]);
          console.log(`数据库中设备状态已更新为离线: ${deviceId} (用户${userId})`);
        } catch (dbError) {
          console.error('更新数据库设备状态失败:', dbError);
        }
      }

      // 通知前端设备断开 - 使用与其他API一致的事件名称
      io.emit('device_status_changed', {
        type: 'device_disconnected',
        deviceId,
        deviceName,
        timestamp: new Date(),
        reason: 'device_request'
      });

      console.log(`设备主动断开处理完成: ${deviceId}`);

      res.json({
        success: true,
        message: '设备断开请求已处理',
        data: {
          deviceId,
          deviceFound,
          disconnectedAt: new Date()
        }
      });

    } catch (error) {
      console.error(`处理设备 ${deviceId} 主动断开失败:`, error);
      res.status(500).json({
        success: false,
        message: '处理断开请求失败: ' + error.message
      });
    }
  });

  // 设备结果上报API - 改为设备认证模式
  app.post('/api/device/:deviceId/result', authenticateDevice(pool), async (req, res) => {
    const { deviceId } = req.params;
    const { logId, result, status } = req.body;
    const { userId, username } = req.device; // 来自设备认证中间件

    console.log(`[脚本结果] 设备${deviceId}(用户${userId})上报结果: logId=${logId}, status=${status}`);

    // 更新日志
    const log = logs.find(l => l.id === logId);
    if (log) {
      log.result = result;
      log.status = status;
      log.completed_at = new Date();
    }

    console.log(`设备执行结果: ${deviceId}, 状态: ${status}`);

    res.json({
      success: true,
      message: '结果已记录'
    });
  });

  console.log('✅ 设备命令管理模块设置完成');

  // 返回设备命令管理相关函数供其他模块使用
  return {
    // 可以在这里返回一些设备命令管理相关的工具函数
  };
}

module.exports = { setupServerDeviceCommands };
