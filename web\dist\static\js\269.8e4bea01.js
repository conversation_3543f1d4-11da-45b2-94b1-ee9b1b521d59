"use strict";(self["webpackChunkautojs_web_control"]=self["webpackChunkautojs_web_control"]||[]).push([[269],{2269:function(e,t,s){s.r(t),s.d(t,{default:function(){return c}});var a=function(){var e=this,t=e._self._c;return t("div",{staticClass:"xianyu-logs"},[e._m(0),t("el-card",{staticClass:"filter-card",attrs:{shadow:"never"}},[t("el-form",{attrs:{model:e.filterForm,inline:""}},[t("el-form-item",{attrs:{label:"功能类型"}},[t("el-select",{attrs:{placeholder:"全部功能",clearable:""},model:{value:e.filterForm.functionType,callback:function(t){e.$set(e.filterForm,"functionType",t)},expression:"filterForm.functionType"}},[t("el-option",{attrs:{label:"关键词私信",value:"keywordMessage"}})],1)],1),t("el-form-item",{attrs:{label:"设备"}},[t("el-select",{attrs:{placeholder:"全部设备",clearable:""},model:{value:e.filterForm.deviceId,callback:function(t){e.$set(e.filterForm,"deviceId",t)},expression:"filterForm.deviceId"}},e._l(e.allDevices,function(e){return t("el-option",{key:e.device_id,attrs:{label:e.device_name||e.device_id,value:e.device_id}})}),1)],1),t("el-form-item",{attrs:{label:"执行状态"}},[t("el-select",{attrs:{placeholder:"全部状态",clearable:""},model:{value:e.filterForm.executionStatus,callback:function(t){e.$set(e.filterForm,"executionStatus",t)},expression:"filterForm.executionStatus"}},[t("el-option",{attrs:{label:"等待中",value:"waiting"}}),t("el-option",{attrs:{label:"执行中",value:"running"}}),t("el-option",{attrs:{label:"已完成",value:"completed"}}),t("el-option",{attrs:{label:"已失败",value:"failed"}}),t("el-option",{attrs:{label:"已停止",value:"stopped"}})],1)],1),t("el-form-item",{attrs:{label:"时间范围"}},[t("el-date-picker",{attrs:{type:"datetimerange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"yyyy-MM-dd HH:mm:ss","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary"},on:{click:e.loadLogs}},[e._v("搜索")]),t("el-button",{on:{click:e.resetFilter}},[e._v("重置")]),t("el-button",{attrs:{type:"warning",loading:e.stoppingAll},on:{click:e.stopAllTasks}},[t("i",{staticClass:"el-icon-video-pause"}),e._v(" 终止所有任务 ")]),t("el-button",{attrs:{type:"danger",loading:e.clearingLogs},on:{click:e.clearAllLogs}},[t("i",{staticClass:"el-icon-delete"}),e._v(" 清空日志 ")])],1)],1)],1),t("el-card",{staticClass:"logs-card"},[t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:e.logs,stripe:"","default-sort":{prop:"createdAt",order:"descending"}}},[t("el-table-column",{attrs:{prop:"id",label:"ID",width:"80",sortable:""}}),t("el-table-column",{attrs:{prop:"functionType",label:"功能类型",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"small"}},[e._v(" "+e._s(e.getFunctionTypeName(s.row.functionType))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"deviceInfo",label:"设备信息",width:"150"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("div",[t("div",[e._v(e._s(s.row.deviceInfo.name||s.row.deviceInfo.id))]),t("div",{staticStyle:{color:"#909399","font-size":"12px"}},[e._v(e._s(s.row.deviceInfo.ip))])])]}}])}),t("el-table-column",{attrs:{prop:"selectedApp",label:"应用版本",width:"120"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{size:"small",type:"info"}},[e._v(" "+e._s(s.row.selectedApp||s.row.configParams?.selectedApp||"默认")+" ")])]}}])}),t("el-table-column",{attrs:{prop:"executionStatus",label:"执行状态",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-tag",{attrs:{type:e.getStatusTagType(s.row.executionStatus),size:"small"}},[e._v(" "+e._s(e.getStatusText(s.row.executionStatus))+" ")])]}}])}),t("el-table-column",{attrs:{prop:"progress",label:"进度",width:"100"},scopedSlots:e._u([{key:"default",fn:function(s){return[t("el-progress",{attrs:{percentage:s.row.progress||0,status:e.getProgressStatus(s.row.executionStatus),"stroke-width":6}})]}}])}),t("el-table-column",{attrs:{prop:"configParams",label:"配置参数","min-width":"200"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.configParams?t("div",[t("div",[t("strong",[e._v("关键词:")]),e._v(" "+e._s(s.row.configParams.keyword))]),t("div",[t("strong",[e._v("私信内容:")]),e._v(" "+e._s(s.row.configParams.message))]),t("div",[t("strong",[e._v("目标数量:")]),e._v(" "+e._s(s.row.configParams.targetCount))])]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"executionResult",label:"执行结果","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(s){return[s.row.executionResult?t("div",[t("div",[t("strong",[e._v("成功私信:")]),e._v(" "+e._s(s.row.executionResult.successCount||0))]),t("div",[t("strong",[e._v("失败数量:")]),e._v(" "+e._s(s.row.executionResult.failureCount||0))]),s.row.executionResult.errorMessage?t("div",{staticStyle:{color:"#F56C6C"}},[t("strong",[e._v("错误:")]),e._v(" "+e._s(s.row.executionResult.errorMessage)+" ")]):e._e()]):e._e()]}}])}),t("el-table-column",{attrs:{prop:"executionTime",label:"执行时长",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatDuration(t.row.executionTime))+" ")]}}])}),t("el-table-column",{attrs:{prop:"createdAt",label:"创建时间",width:"160",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatTime(t.row.createdAt))+" ")]}}])}),t("el-table-column",{attrs:{label:"操作",width:"150",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(s){return["running"===s.row.executionStatus?t("el-button",{attrs:{type:"danger",size:"mini"},on:{click:function(t){return e.stopExecution(s.row)}}},[e._v(" 停止 ")]):e._e(),["completed","failed","stopped"].includes(s.row.executionStatus)?t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.retryExecution(s.row)}}},[e._v(" 再来一次 ")]):e._e(),t("el-button",{attrs:{type:"info",size:"mini"},on:{click:function(t){return e.viewDetails(s.row)}}},[e._v(" 详情 ")])]}}])})],1),t("div",{staticClass:"pagination-wrapper"},[t("el-pagination",{attrs:{"current-page":e.currentPage,"page-sizes":[10,20,50,100],"page-size":e.pageSize,layout:"total, sizes, prev, pager, next, jumper",total:e.total},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)],1),t("el-dialog",{attrs:{title:"执行详情",visible:e.detailDialogVisible,width:"60%","before-close":e.handleDetailClose},on:{"update:visible":function(t){e.detailDialogVisible=t}}},[e.selectedLog?t("div",[t("el-descriptions",{attrs:{column:2,border:""}},[t("el-descriptions-item",{attrs:{label:"执行ID"}},[e._v(e._s(e.selectedLog.id))]),t("el-descriptions-item",{attrs:{label:"功能类型"}},[e._v(e._s(e.getFunctionTypeName(e.selectedLog.functionType)))]),t("el-descriptions-item",{attrs:{label:"设备信息"}},[e._v(e._s(e.selectedLog.deviceInfo.name||e.selectedLog.deviceInfo.id))]),t("el-descriptions-item",{attrs:{label:"执行状态"}},[e._v(e._s(e.getStatusText(e.selectedLog.executionStatus)))]),t("el-descriptions-item",{attrs:{label:"进度"}},[e._v(e._s(e.selectedLog.progress||0)+"%")]),t("el-descriptions-item",{attrs:{label:"执行时长"}},[e._v(e._s(e.formatDuration(e.selectedLog.executionTime)))])],1),t("div",{staticStyle:{"margin-top":"20px"}},[t("h4",[e._v("配置参数")]),t("pre",[e._v(e._s(JSON.stringify(e.selectedLog.configParams,null,2)))])]),e.selectedLog.executionResult?t("div",{staticStyle:{"margin-top":"20px"}},[t("h4",[e._v("执行结果")]),t("pre",[e._v(e._s(JSON.stringify(e.selectedLog.executionResult,null,2)))])]):e._e(),e.selectedLog.debugLogs&&e.selectedLog.debugLogs.length>0?t("div",{staticStyle:{"margin-top":"20px"}},[t("h4",[e._v("调试日志")]),t("div",{staticClass:"debug-logs"},e._l(e.selectedLog.debugLogs,function(s,a){return t("div",{key:a,staticClass:"debug-log-item"},[t("span",{staticClass:"log-time"},[e._v(e._s(e.formatTime(s.timestamp)))]),t("span",{staticClass:"log-message"},[e._v(e._s(s.message))])])}),0)]):e._e()],1):e._e()])],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"page-header"},[t("h2",[e._v("闲鱼执行日志")]),t("p",[e._v("查看闲鱼自动化工具的执行记录和详细日志")])])}],o={name:"XianyuLogs",data(){return{logs:[],loading:!1,total:0,currentPage:1,pageSize:20,filterForm:{functionType:"",deviceId:"",executionStatus:""},dateRange:null,detailDialogVisible:!1,selectedLog:null,refreshTimer:null,stoppingAll:!1,clearingLogs:!1}},computed:{allDevices(){return this.$store.getters["device/devices"]||[]}},async mounted(){await this.loadData(),this.startAutoRefresh()},beforeDestroy(){this.stopAutoRefresh()},methods:{async loadData(){await this.$store.dispatch("device/fetchDevices"),await this.loadLogs()},async loadLogs(){this.loading=!0;try{const e={page:this.currentPage,limit:this.pageSize,...this.filterForm};this.dateRange&&2===this.dateRange.length&&(e.startDate=this.dateRange[0],e.endDate=this.dateRange[1]);const t=await this.$http.get("/api/xianyu/logs",{params:e});t.data.success?(this.logs=t.data.data.data||[],this.total=t.data.data.total||0):this.$message.error("加载日志失败: "+t.data.message)}catch(e){console.error("加载闲鱼执行日志失败:",e),this.$message.error("加载日志失败")}finally{this.loading=!1}},resetFilter(){this.filterForm={functionType:"",deviceId:"",executionStatus:""},this.dateRange=null,this.currentPage=1,this.loadLogs()},handleSizeChange(e){this.pageSize=e,this.currentPage=1,this.loadLogs()},handleCurrentChange(e){this.currentPage=e,this.loadLogs()},getFunctionTypeName(e){const t={keywordMessage:"关键词私信"};return t[e]||e},getStatusText(e){const t={waiting:"等待中",running:"执行中",completed:"已完成",failed:"已失败",stopped:"已停止"};return t[e]||e},getStatusTagType(e){const t={waiting:"info",running:"warning",completed:"success",failed:"danger",stopped:"info"};return t[e]||"info"},getProgressStatus(e){return"completed"===e?"success":"failed"===e?"exception":null},formatTime(e){return e?new Date(e).toLocaleString("zh-CN"):"-"},formatDuration(e){if(!e)return"-";const t=Math.floor(e/3600),s=Math.floor(e%3600/60),a=e%60;return t>0?`${t}时${s}分${a}秒`:s>0?`${s}分${a}秒`:`${a}秒`},async stopExecution(e){try{console.log("闲鱼日志: 停止执行任务",e);const s=await this.$http.post("/api/xianyu/stop",{logId:e.id,deviceId:e.deviceInfo.id});if(s.data.success){this.$message.success("停止成功"),console.log("闲鱼日志: 重置前端状态"),this.$store.dispatch("xianyu/clearDeviceExecutionState",e.deviceInfo.id),console.log("闲鱼日志: 强制重置所有功能状态"),this.$store.dispatch("xianyu/resetAllStates"),this.$store.dispatch("device/updateDeviceStatus",{deviceId:e.deviceInfo.id,status:"online",lastSeen:new Date});try{console.log("闲鱼日志: 强制更新设备状态为online"),await this.$http.post("/api/device/force-status",{deviceId:e.deviceInfo.id,status:"online"}),console.log("闲鱼日志: 设备状态强制更新成功")}catch(t){console.error("闲鱼日志: 设备状态强制更新失败:",t)}this.$root.$emit("xianyu-task-stopped",{functionType:e.functionType,deviceId:e.deviceInfo.id,taskId:e.id,message:"任务已停止"}),this.$root.$emit("device-status-updated",{deviceId:e.deviceInfo.id,status:"online"}),await this.loadLogs()}else this.$message.error("停止失败: "+s.data.message)}catch(t){console.error("停止执行失败:",t),this.$message.error("停止失败")}},retryExecution(e){this.$router.push({path:"/xianyu",query:{retry:"true",functionType:e.functionType,deviceId:e.deviceInfo.id,config:JSON.stringify(e.configParams)}})},viewDetails(e){this.selectedLog=e,this.detailDialogVisible=!0},handleDetailClose(){this.detailDialogVisible=!1,this.selectedLog=null},startAutoRefresh(){this.refreshTimer=setInterval(()=>{this.loadLogs()},6e3)},stopAutoRefresh(){this.refreshTimer&&(clearInterval(this.refreshTimer),this.refreshTimer=null)},async stopAllTasks(){this.$confirm("确定要终止所有正在执行的闲鱼任务吗？","确认终止",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{this.stoppingAll=!0;try{const t=await this.$http.post("/api/xianyu/stop-all");if(t.data.success){this.$message.success(t.data.message),console.log("闲鱼日志: 重置所有前端状态"),this.$store.dispatch("xianyu/resetAllStates");try{console.log("闲鱼日志: 强制更新所有忙碌设备状态为online");const e=this.allDevices.filter(e=>"busy"===e.status);for(const t of e)await this.$http.post("/api/device/force-status",{deviceId:t.device_id,status:"online"}),console.log(`闲鱼日志: 设备 ${t.device_id} 状态强制更新成功`)}catch(e){console.error("闲鱼日志: 设备状态强制更新失败:",e)}this.$root.$emit("xianyu-reset-all-states"),await this.loadLogs()}else this.$message.error("终止失败: "+t.data.message)}catch(e){console.error("终止所有任务失败:",e),this.$message.error("终止失败: "+(e.response?.data?.message||e.message))}finally{this.stoppingAll=!1}}).catch(()=>{})},async clearAllLogs(){this.$confirm("确定要清空所有闲鱼执行日志吗？此操作不可恢复！","确认清空",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{this.clearingLogs=!0;try{const e=await this.$http.delete("/api/xianyu/logs");e.data.success?(this.$message.success(e.data.message),this.logs=[],this.total=0,this.currentPage=1):this.$message.error("清空失败: "+e.data.message)}catch(e){console.error("清空日志失败:",e),this.$message.error("清空失败: "+(e.response?.data?.message||e.message))}finally{this.clearingLogs=!1}}).catch(()=>{})}}},l=o,r=s(1656),n=(0,r.A)(l,a,i,!1,null,"5be9ba74",null),c=n.exports}}]);